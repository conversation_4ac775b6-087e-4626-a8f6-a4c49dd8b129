package com.bes.business.Observable;

import java.util.ArrayList;
import java.util.Collection;

/**
 * 属性观察者实现类
 *
 * 功能描述：
 * - 实现观察者设计模式的核心类
 * - 提供事件注册、注销和分发机制
 * - 支持一对多和一对一的事件通知模式
 * - 线程安全的事件管理和分发
 *
 * 设计模式：
 * - 观察者模式（Observer Pattern）的Subject角色
 * - 单例模式，确保全局唯一的事件管理器
 * - 发布-订阅模式，解耦事件发布者和订阅者
 *
 * 核心特性：
 * - 基于MultiHashMap实现一个事件对应多个监听器
 * - 自动线程切换，确保事件回调在主线程执行
 * - 支持动态注册和注销监听器
 * - 提供灵活的事件参数传递机制
 *
 * 使用场景：
 * - 蓝牙连接状态变化通知
 * - UI界面数据更新通知
 * - 业务逻辑间的解耦通信
 * - 跨组件的事件传递
 *
 * 线程安全：
 * - 所有公共方法都使用synchronized关键字保证线程安全
 * - 事件分发自动切换到主线程执行
 * - 支持在任意线程中触发事件
 *
 * <AUTHOR>
 * @version 1.0
 *
 * 原始注释：
 * 消息推送工具类
 * 具备：一对多，一对一，模式
 */
public class PropertyObservable {
	/**
	 * 观察者模式的核心存储结构
	 * 使用MultiHashMap存储EventID到Event监听器的映射关系
	 * 支持一个事件ID对应多个监听器的场景
	 */
	private MultiHashMap<EventID, Event> map = new MultiHashMap<EventID, Event>();

	/**
	 * 单例实例
	 * 确保全局只有一个PropertyObservable实例
	 */
	private static PropertyObservable instance;

	/**
	 * 获取PropertyObservable的单例实例
	 * 使用双重检查锁定模式确保线程安全的单例创建
	 *
	 * @return PropertyObservable的唯一实例
	 *
	 * 原始注释：对外，统一提供单例
	 */
	public synchronized static PropertyObservable getInstance(){
		if(instance == null){ // 第一次检查
			instance = new PropertyObservable(); // 创建实例
		}
		return instance;
	}

	/**
	 * 私有构造方法
	 * 防止外部直接创建实例，确保单例模式的实现
	 */
	private PropertyObservable() {}
	/**
	 * 获取指定事件ID的监听器数量
	 *
	 * @param eventId 要查询的事件ID
	 * @return 监听该事件的监听器数量，如果没有监听器则返回0
	 *
	 * 原始注释：或取对eventId这个消息监听的数目
	 */
	public int getListenerCount(EventID eventId) {
		ArrayList<Event> ls = map.get(eventId); // 获取事件对应的监听器列表
		if (null != ls) { // 如果列表存在
			return ls.size(); // 返回列表大小
		}
		return 0; // 没有监听器时返回0
	}

	/**
	 * 检查指定事件ID是否有监听器
	 *
	 * @param eventId 要检查的事件ID
	 * @return 如果有监听器则返回true，否则返回false
	 *
	 * 原始注释：是否有对eventId这个消息的监听
	 */
	public boolean hasListener(EventID eventId) {
		return getListenerCount(eventId) > 0; // 监听器数量大于0表示有监听器
	}

	/**
	 * 获取当前管理的事件类型数量
	 * 注意：这里返回的是不同事件ID的数量，不是监听器的总数量
	 *
	 * @return 事件类型的数量
	 */
	public int size() {
		return map.size(); // 返回MultiHashMap中键的数量
	}
	// ==================== 监听器注册方法 ====================

	/**
	 * 注册单个监听器（已注释的方法）
	 * 此方法会替换指定事件的所有监听器，与addListener不同
	 *
	 * 原始注释：
	 * 注册单个监听器
	 * @param listener
	 * @param eventIds
	 */
//	public synchronized void setListener(ObservableID eventId, Event listener) {
//		if(null==listener) return;
//		map.remove(eventId); // 先移除现有监听器
//		map.put(eventId, listener); // 再添加新监听器
//	}

	/**
	 * 批量注册监听器
	 * 为多个事件ID注册同一个监听器，支持一个监听器监听多个事件
	 *
	 * @param listener 要注册的事件监听器，不能为null
	 * @param eventIds 要监听的事件ID数组，不能为null
	 *
	 * 原始注释：
	 * 注册多个监听器
	 * @param listener
	 * @param eventIds
	 */
	 public synchronized void addListener(Event listener, EventID eventIds[]) {
		if(null==listener||null==eventIds) return; // 参数校验
		for (EventID id : eventIds) { // 遍历所有事件ID
			map.put(id, listener); // 为每个事件ID添加监听器
		}
	}

	/**
	 * 注册单个监听器
	 * 为指定事件ID添加一个监听器，支持同一事件多个监听器
	 *
	 * @param listener 要注册的事件监听器，不能为null
	 * @param eventId 要监听的事件ID
	 *
	 * 原始注释：
	 * 注册单个监听器
	 * @param listener
	 * @param eventId
	 */
	public synchronized void addListener(Event listener, EventID eventId) {
		if(null==listener) return; // 参数校验
		map.put(eventId, listener); // 添加监听器到指定事件
	}
	// ==================== 监听器移除方法 ====================

	/**
	 * 批量移除监听器
	 * 从指定的多个事件ID中移除特定监听器
	 *
	 * @param listener 要移除的监听器，不能为null
	 * @param eventIds 要移除监听器的事件ID数组，支持可变参数
	 *
	 * 原始注释：
	 * 移除多个
	 * @param listener
	 * @param eventIds
	 */
	public synchronized void removeListener(Event listener, EventID... eventIds) {
		if(null==listener||null==eventIds) return; // 参数校验
		for (EventID id : eventIds) { // 遍历所有事件ID
			map.remove(id, listener); // 从每个事件中移除指定监听器
		}
	}

	/**
	 * 移除监听器的所有注册
	 * 从所有事件中移除指定的监听器
	 * 注意：低效率操作，需要遍历所有事件，时间复杂度为O(n)
	 *
	 * @param listener 要完全移除的监听器，不能为null
	 *
	 * 原始注释：
	 * 低效率, 需要遍历
	 * @param listener
	 */
	public synchronized void removeListener(Event listener) {
		if(null==listener) return; // 参数校验
		map.removeValue(listener); // 从所有事件中移除该监听器
	}

	/**
	 * 清空所有监听器
	 * 移除所有事件的所有监听器，重置观察者状态
	 */
	public synchronized void clearListeners() {
		map.clear(); // 清空所有监听器映射
	}
	// ==================== 事件分发方法 ====================

	/**
	 * 触发事件并通知所有监听器
	 * 这是观察者模式的核心方法，负责将事件分发给所有注册的监听器
	 *
	 * 线程安全特性：
	 * - 本方法可在子线程或主线程调用
	 * - 最终的监听器回调会自动切换到主线程执行
	 * - 使用synchronized确保监听器列表的线程安全访问
	 *
	 * 执行流程：
	 * 1. 同步获取事件对应的监听器列表
	 * 2. 复制监听器数组，避免在回调过程中列表被修改
	 * 3. 通过Handler将每个监听器的回调切换到主线程执行
	 * 4. 异常处理确保单个监听器异常不影响其他监听器
	 *
	 * @param eventId 事件ID，标识要触发的事件类型
	 * @param baseInfo 基础信息实体类，包含事件相关的基础数据
	 * @param list 信息列表集合，当事件涉及多个数据对象时使用
	 * @param args 其他类型的可变参数，提供灵活的数据传递方式
	 *
	 * 原始注释：
	 * 本方法可在子线程或主线程调用，但是最终结果回调为主线程
	 * @param eventId 事件id
	 * @param baseInfo 返回结果实体类
	 * @param list 返回结果实体类
	 * @param args 其他类型的结果
	 */
	public void fireEvent(final EventID eventId, final BaseInfo baseInfo, final Collection<? extends BaseInfo> list, final Object... args) {
		int size = 0;
		Event[] arrays = null;

		// 同步块：安全地获取监听器列表并复制到数组中
		synchronized (this) {
			ArrayList<Event> ls = map.get(eventId); // 获取事件对应的监听器列表
			if (null ==ls) { // 如果没有监听器
				return; // 直接返回，不进行任何处理
			}
			size = ls.size(); // 获取监听器数量
			arrays = new Event[size]; // 创建监听器数组
			ls.toArray(arrays); // 将列表复制到数组中，避免并发修改问题
		}

		// 遍历所有监听器并在主线程中执行回调
		if (null != arrays) {
			for (final Event observer : arrays) { // 遍历监听器数组
				if (null != observer) { // 检查监听器是否为空
					try {
						// 通过Handler将回调切换到主线程执行
						AppConfig.mHandler.post(new Runnable() {
							@Override
							public void run() {
								// 在主线程中调用监听器的updateView方法
								observer.updateView(eventId, baseInfo, list, args);
							}
						});
					} catch (Exception e) {
						// 异常处理：确保单个监听器的异常不影响其他监听器
						e.printStackTrace();
					}
				}
			}
		}
	}
}
