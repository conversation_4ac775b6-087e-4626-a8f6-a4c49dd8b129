
package com.bes;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.PackageManager.NameNotFoundException;
import android.os.Build;
import android.os.Environment;
import android.util.Log;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.io.Writer;
import java.lang.Thread.UncaughtExceptionHandler;
import java.lang.reflect.Field;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 崩溃异常处理器
 *
 * 功能描述：
 * - 实现UncaughtExceptionHandler接口，处理未捕获的异常
 * - 收集设备信息和异常详情，生成详细的崩溃报告
 * - 将崩溃信息保存到本地文件，便于后续分析和调试
 * - 提供完整的异常处理流程，避免应用直接崩溃退出
 *
 * 设计模式：
 * - 单例模式：确保全局只有一个异常处理器实例
 * - 策略模式：可以替换系统默认的异常处理策略
 * - 模板方法模式：定义异常处理的标准流程
 *
 * 核心功能：
 * - 异常拦截：捕获所有未处理的运行时异常
 * - 信息收集：收集设备硬件、系统版本、应用版本等信息
 * - 日志记录：将异常堆栈和环境信息写入日志文件
 * - 文件管理：自动创建目录结构，按时间戳命名日志文件
 *
 * 使用场景：
 * - 应用程序全局异常处理
 * - 崩溃日志收集和分析
 * - 提高应用稳定性和用户体验
 * - 开发阶段的调试和问题定位
 *
 * 文件存储：
 * - 存储路径：/mnt/sdcard/BES_voice/crash/
 * - 文件命名：crash_时间戳.txt格式
 * - 内容格式：设备信息 + 异常堆栈信息
 *
 * <AUTHOR>
 * @version 1.0
 */
public class CrashExceptionHandler implements UncaughtExceptionHandler {

    /** 日志标签，用于调试和日志输出 */
    public static final String TAG = "CrashHandler";

    /**
     * 系统默认的UncaughtException处理类
     * 保存原有的异常处理器，在处理完自定义逻辑后可以调用原处理器
     */
    private UncaughtExceptionHandler mDefaultHandler;

    /**
     * CrashExceptionHandler单例实例
     * 使用懒汉式单例模式，在首次调用时创建实例
     */
    private static CrashExceptionHandler instance;

    /**
     * 程序的Context对象
     * 用于获取应用信息、包管理器等操作
     */
    private Context mContext;

    /**
     * 设备信息和异常信息存储容器
     * 用于收集设备硬件信息、系统版本、应用版本等环境信息
     */
    private Map<String, String> infos = new HashMap<String, String>();

    /**
     * 日期格式化器
     * 用于格式化日期，作为崩溃日志文件名的一部分
     * 格式：yyyy-MM-dd-HH-mm-ss（年-月-日-时-分-秒）
     */
    private DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss");

    /**
     * 私有构造方法
     * 保证只有一个CrashExceptionHandler实例，实现单例模式
     */
    private CrashExceptionHandler() {}


    /**
     * 获取CrashExceptionHandler实例
     * 单例模式的访问方法，使用懒加载确保线程安全
     *
     * @return CrashExceptionHandler的唯一实例
     */
    public static CrashExceptionHandler getInstance() {
        if(instance == null)  // 懒加载检查
            instance = new CrashExceptionHandler(); // 创建实例
        return instance;
    }

    // ==================== 初始化和核心异常处理方法 ====================

    /**
     * 初始化异常处理器
     * 设置当前处理器为系统默认的异常处理器，接管所有未捕获异常
     *
     * @param context 应用程序上下文，用于获取应用信息和显示提示
     */
    public void init(Context context) {
        mContext = context; // 保存上下文引用

        // 获取系统默认的UncaughtException处理器，保留原有处理逻辑
        mDefaultHandler = Thread.getDefaultUncaughtExceptionHandler();

        // 设置当前CrashExceptionHandler为程序的默认异常处理器
        Thread.setDefaultUncaughtExceptionHandler(this);
    }

    /**
     * 未捕获异常处理入口方法
     * 当应用程序发生未捕获异常时，系统会自动调用此方法
     *
     * 处理流程：
     * 1. 尝试使用自定义异常处理逻辑
     * 2. 如果自定义处理失败，则调用系统默认处理器
     * 3. 如果自定义处理成功，则终止应用程序进程
     *
     * @param thread 发生异常的线程
     * @param ex 未捕获的异常对象
     */
    @Override
    public void uncaughtException(Thread thread, Throwable ex) {
        if (!handleException(ex) && mDefaultHandler != null) {
            // 如果自定义处理失败且系统默认处理器存在，则使用系统默认处理器
            mDefaultHandler.uncaughtException(thread, ex);
        } else {
            // 自定义处理成功，主动终止程序进程
            android.os.Process.killProcess(android.os.Process.myPid()); // 杀死当前进程
            System.exit(1); // 退出JVM，返回错误状态码1
        }
    }

    /**
     * 自定义异常处理核心方法
     * 收集错误信息、设备信息，并保存到文件中
     *
     * 处理步骤：
     * 1. 验证异常对象的有效性
     * 2. 收集设备和应用程序信息
     * 3. 将异常信息保存到文件
     *
     * @param ex 要处理的异常对象
     * @return true表示成功处理了该异常信息，false表示处理失败
     *
     * 原始注释：
     * 自定义错误处理,收集错误信息 发送错误报告等操作均在此完成.
     */
    synchronized private boolean handleException(Throwable ex) {
        if (ex == null) { // 异常对象为空，无法处理
            return false;
        }

        // 收集设备参数信息，包括硬件信息、系统版本、应用版本等
        collectDeviceInfo(mContext);

        // 将异常信息和设备信息保存到文件中
        saveCatchInfo2File(ex);

        return true; // 返回处理成功标志
    }

    // ==================== 设备信息收集方法 ====================

    /**
     * 收集设备参数信息
     * 收集应用版本信息和设备硬件信息，用于崩溃日志分析
     *
     * 收集的信息包括：
     * - 应用版本名称和版本号
     * - 设备硬件信息（通过反射获取Build类的所有字段）
     * - 系统版本、设备型号、制造商等信息
     *
     * @param ctx 应用程序上下文，用于获取包管理器和应用信息
     */
    public void collectDeviceInfo(Context ctx) {
        try {
            PackageManager pm = ctx.getPackageManager(); // 获取包管理器
            PackageInfo pi = pm.getPackageInfo(ctx.getPackageName(), PackageManager.GET_ACTIVITIES); // 获取应用包信息
            if (pi != null) { // 如果包信息存在
                // 获取版本名称，如果为null则设置为"null"字符串
                String versionName = pi.versionName == null ? "null" : pi.versionName;
                String versionCode = pi.versionCode + ""; // 获取版本号并转换为字符串
                infos.put("versionName", versionName); // 存储版本名称
                infos.put("versionCode", versionCode); // 存储版本号
            }
        } catch (NameNotFoundException e) {
            Log.e(TAG, "an error occurred when collect package info", e); // 记录包信息收集错误
        }

        // 通过反射获取Build类的所有字段，收集设备硬件和系统信息
        Field[] fields = Build.class.getDeclaredFields();
        for (Field field : fields) { // 遍历所有字段
            try {
                field.setAccessible(true); // 设置字段可访问（包括私有字段）
                infos.put(field.getName(), field.get(null).toString()); // 获取字段值并存储
                Log.d(TAG, field.getName() + " : " + field.get(null)); // 输出调试日志
            } catch (Exception e) {
                Log.e(TAG, "an error occurred when collect crash info", e); // 记录字段访问错误
            }
        }
    }

    // ==================== 文件保存和日志处理方法 ====================

    /**
     * 保存崩溃信息到文件中
     * 将收集的设备信息和异常堆栈信息格式化后保存到外部存储
     *
     * 文件内容格式：
     * 1. 设备信息部分：key=value格式，每行一个信息项
     * 2. 异常堆栈部分：完整的异常堆栈跟踪信息
     * 3. 异常链信息：包含所有cause异常的堆栈信息
     *
     * 文件存储策略：
     * - 存储路径：/mnt/sdcard/BES_voice/crash/
     * - 文件命名：crash_时间戳.txt
     * - 自动创建目录结构
     * - 仅在外部存储可用时保存
     *
     * @param ex 要保存的异常对象，包含完整的异常信息
     * @return 返回保存的文件名称，便于后续文件传送到服务器，失败时返回null
     *
     * 原始注释：
     * 保存错误信息到文件中
     * @return  返回文件名称,便于将文件传送到服务器
     */
    public String saveCatchInfo2File(Throwable ex) {

        StringBuffer sb = new StringBuffer(); // 创建字符串缓冲区

        // 第一部分：添加设备信息和应用信息
        for (Map.Entry<String, String> entry : infos.entrySet()) {
            String key = entry.getKey(); // 获取信息项的键
            String value = entry.getValue(); // 获取信息项的值
            sb.append(key + "=" + value + "\n"); // 格式化为key=value形式并换行
        }

        // 第二部分：添加异常堆栈信息
        Writer writer = new StringWriter(); // 创建字符串写入器
        PrintWriter printWriter = new PrintWriter(writer); // 创建打印写入器
        ex.printStackTrace(printWriter); // 将异常堆栈写入到字符串

        // 第三部分：添加异常链信息（cause异常）
        Throwable cause = ex.getCause(); // 获取引起当前异常的原因异常
        while (cause != null) { // 遍历整个异常链
            cause.printStackTrace(printWriter); // 打印cause异常的堆栈
            cause = cause.getCause(); // 获取下一个cause异常
        }
        printWriter.close(); // 关闭打印写入器
        String result = writer.toString(); // 获取完整的异常堆栈字符串
        sb.append(result); // 将异常堆栈添加到缓冲区

        Log.e(TAG , sb.toString()); // 在LogCat中输出完整的崩溃信息

        try {
            String time = formatter.format(new Date()); // 格式化当前时间
            String fileName = "crash_" + time  + ".txt"; // 生成文件名

            // 检查外部存储是否可用
            if (Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED)) {

                // 创建主目录：/mnt/sdcard/BES_voice/
                String path = "/mnt/sdcard/BES_voice/";
                File dir = new File(path);
                if (!dir.exists()) { // 如果目录不存在
                    dir.mkdirs(); // 创建目录结构
                }

                // 创建崩溃日志子目录：/mnt/sdcard/BES_voice/crash/
                path = "/mnt/sdcard/BES_voice/crash/";
                dir = new File(path);
                if (!dir.exists()) { // 如果崩溃目录不存在
                    dir.mkdirs(); // 创建崩溃目录
                }

                // 将崩溃信息写入文件
                FileOutputStream fos = new FileOutputStream(path + fileName);
                fos.write(sb.toString().getBytes()); // 将字符串转换为字节数组并写入
                fos.close(); // 关闭文件输出流
            }
            return fileName; // 返回文件名
        } catch (Exception e) {
            Log.e(TAG, "an error occurred while writing file...", e); // 记录文件写入错误
        }
        return null; // 保存失败时返回null
    }

    // ==================== 日志发送方法（预留功能） ====================

    /**
     * 将捕获的导致崩溃的错误信息发送给开发人员
     *
     * 功能说明：
     * - 读取保存的崩溃日志文件
     * - 将文件内容逐行输出到LogCat
     * - 为后续发送到服务器预留接口
     *
     * 当前实现：
     * - 仅将日志保存在外部存储和输出到LogCat中
     * - 尚未实现发送到后台服务器的功能
     * - 使用GBK编码读取文件内容
     *
     * 扩展方向：
     * - 可以添加网络发送功能
     * - 可以添加邮件发送功能
     * - 可以添加用户反馈收集功能
     *
     * @param fileName 要发送的崩溃日志文件名（完整路径）
     *
     * 原始注释：
     * 目前只将log日志保存在外部存储和输出到LogCat中，并未发送给后台。
     */
    private void sendCrashLog2PM(String fileName){
        if(!new File(fileName).exists()){ // 检查文件是否存在
            // 注释掉的用户提示：Toast.makeText(mContext, "日志文件不存在！", Toast.LENGTH_SHORT).show();
            return; // 文件不存在则直接返回
        }

        FileInputStream fis = null; // 文件输入流
        BufferedReader reader = null; // 缓冲读取器
        String s = null; // 临时字符串变量

        try {
            fis = new FileInputStream(fileName); // 创建文件输入流
            reader = new BufferedReader(new InputStreamReader(fis, "GBK")); // 使用GBK编码创建读取器
            while(true){ // 循环读取文件内容
                s = reader.readLine(); // 读取一行
                if(s == null) break; // 如果读取到文件末尾则退出循环

                // 由于目前尚未确定以何种方式发送，所以先打出log日志
                Log.i("info", s.toString()); // 将每行内容输出到LogCat
            }
        } catch (FileNotFoundException e) { // 文件未找到异常
            e.printStackTrace();
        } catch (IOException e) { // IO异常
            e.printStackTrace();
        }finally{ // 资源清理，确保流被正确关闭
            try {
                reader.close(); // 关闭读取器
                fis.close(); // 关闭文件输入流
            } catch (IOException e) {
                e.printStackTrace(); // 记录关闭流时的异常
            }
        }
    }
}