package com.bes_lib.bluetooth;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothSocket;
import android.content.SharedPreferences;
import android.util.Log;


import com.bes_lib.bluetooth.callback.ConnectCallback;
import com.bes_lib.utils.ArrayUtil;
import com.bes_lib.utils.LogUtils;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import androidx.annotation.NonNull;

/**
 * SPP蓝牙连接器
 *
 * 该类实现了SPP（Serial Port Profile）蓝牙连接的完整功能，主要特性包括：
 * 1. 单例模式的连接管理器
 * 2. 异步的蓝牙连接和数据传输
 * 3. 多回调接口的支持和管理
 * 4. 连接状态的实时监控和通知
 * 5. 线程安全的数据读写操作
 * 6. 自动的连接异常处理和恢复
 *
 * 设计特点：
 * - 单例模式：确保全局唯一的SPP连接实例
 * - 异步操作：连接和数据传输都在独立线程中执行
 * - 状态管理：完整的连接状态跟踪和转换
 * - 回调机制：支持多个监听器同时监听连接事件
 * - 线程安全：关键操作使用同步锁保护
 *
 * 使用场景：
 * - SPP蓝牙设备的数据通信
 * - 音频设备的控制和数据传输
 * - 传感器数据的实时采集
 * - 设备配置和状态查询
 *
 * 连接流程：
 * 1. 获取单例实例
 * 2. 添加连接回调监听器
 * 3. 调用connect()方法建立连接
 * 4. 通过write()方法发送数据
 * 5. 通过回调接收数据和状态变化
 * 6. 调用disconnect()方法断开连接
 *
 * 注意事项：
 * - 需要BLUETOOTH和BLUETOOTH_ADMIN权限
 * - 连接操作会阻塞，建议在子线程中调用
 * - 数据传输采用1944字节的缓冲区
 * - 连接异常时会自动触发断开流程
 *
 * Created by alloxuweibin on 2017/12/11.
 */
public class SppConnector {

    /**
     * 日志标签，使用类的简单名称
     */
    private final String TAG = getClass().getSimpleName();

    /**
     * SPP服务的标准UUID
     * 这是SPP（Serial Port Profile）的标准服务UUID，
     * 用于建立RFCOMM连接通道
     */
    public static final UUID sUUID = UUID.fromString("00001101-0000-1000-8000-00805F9B34FB");

    /**
     * 单例实例，使用volatile确保多线程可见性
     */
    private static volatile SppConnector sConnector;

    /**
     * 蓝牙Socket连接对象
     * 用于实际的蓝牙数据传输
     */
    private BluetoothSocket mBluetoothSocket;

    /**
     * 连接状态常量：正在连接
     */
    private static final int STATE_CONNECTING = 1;

    /**
     * 连接状态常量：已连接
     */
    private static final int STATE_CONNECTED = 2;

    /**
     * 连接状态常量：已断开
     */
    private static final int STATE_DISCONNECTED = 0;

    /**
     * 当前连接状态
     * 默认为断开状态
     */
    private int mConnState = STATE_DISCONNECTED;

    /**
     * 回调操作的同步锁
     * 保护回调列表的线程安全访问
     */
    private Object mCallbackLock = new Object();

    /**
     * 主要的连接回调接口
     * 用于接收连接状态变化和数据
     */
    private ConnectCallback mCallback;

    public static SppConnector getConnector() {
        if (sConnector == null) {
            synchronized (SppConnector.class) {
                if (sConnector == null) {
                    sConnector = new SppConnector();
                }
            }
        }
        return sConnector;
    }

    public boolean connect(String address){
        return connect(BluetoothAdapter.getDefaultAdapter().getRemoteDevice(address));
    }

    public boolean connect(@NonNull BluetoothDevice device) {
        if (mConnState == STATE_CONNECTING || mConnState == STATE_CONNECTED) {
            return false;
        }
        new Thread(new ConnectRunnable(device)).start();
        return true;
    }

    public void disconnect() {
        try {
            if (mBluetoothSocket != null)
                mBluetoothSocket.close();
            onConnectionStateChanged(false);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public boolean write(byte[] data) {
        if (mConnectedRunnable != null) {
            return mConnectedRunnable.write(data);
        }
        return false;
    }

    private List<ConnectCallback> mConnectCallbacks = new ArrayList<>();

    public void addConnectCallback(ConnectCallback callback) {
        mCallback = callback;
        synchronized (mCallbackLock) {
            if (!mConnectCallbacks.contains(callback))
                mConnectCallbacks.add(callback);
        }
    }

    public void removeConnectCallback(ConnectCallback callback) {
        synchronized (mCallbackLock) {
            mConnectCallbacks.remove(callback);
        }
    }

    public void removeAllCallBack(){
        synchronized (mCallbackLock) {
            mConnectCallbacks.clear();
        }
    }

    private void onConnectionStateChanged(boolean connected) {
        Log.i(TAG, "spp onConnectionStateChanged: ------" + connected);
        synchronized (mCallbackLock) {
            if (connected && mConnState != STATE_CONNECTED) {
                Log.i(TAG, "mCallback: --" + mCallback);
                mCallback.onConnectionStateChanged(true);
//                for (ConnectCallback callback : mConnectCallbacks) {
//                    callback.onConnectionStateChanged(true);
//                }
                mConnState = STATE_CONNECTED;
            } else if (!connected && mConnState != STATE_DISCONNECTED) {
                mBluetoothSocket = null;
                mConnectedRunnable = null;
                mConnState = STATE_DISCONNECTED;
                mCallback.onConnectionStateChanged(false);
//                for (ConnectCallback callback : mConnectCallbacks)
//                    callback.onConnectionStateChanged(false);
            }
        }
    }

    public boolean isConnected() {
        return mConnState == STATE_CONNECTED;
    }

    private void onReceive(byte[] data) {
        synchronized (mCallbackLock) {
            for (ConnectCallback callback : mConnectCallbacks)
                callback.onReceive(null , data);
        }
    }

    private ConnectedRunnable mConnectedRunnable;

    private class ConnectRunnable implements Runnable {

        private BluetoothDevice mDevice;

        public ConnectRunnable(BluetoothDevice device) {
            mDevice = device;
        }

        @Override
        public void run() {
            LOG("ConnectRunnable run()");
            try {
                mConnState = STATE_CONNECTING;
                mBluetoothSocket = mDevice.createInsecureRfcommSocketToServiceRecord(sUUID);
                mBluetoothSocket.connect();
                mConnectedRunnable = new ConnectedRunnable(mBluetoothSocket.getInputStream(), mBluetoothSocket.getOutputStream());
                onConnectionStateChanged(true);
                new Thread(mConnectedRunnable).start();
            } catch (IOException e) {
                e.printStackTrace();
                onConnectionStateChanged(false);
            }
        }
    }

    private class ConnectedRunnable implements Runnable {

        private OutputStream mWrite;
        private InputStream mRead;
        private ArrayList<InputStream> mReadArr = new ArrayList<>();

        public ConnectedRunnable(InputStream read, OutputStream write) {
            mRead = read;
            mWrite = write;
        }

        @Override
        public void run() {
            LOG("ConnectedRunnable run()");
            try {
                byte[] data = new byte[1944];
                while (true) {
                    int length = mRead.read(data);
                    if(length > 0){
                        onReceive(ArrayUtil.extractBytes(data, 0, length));
                    }
                }
            } catch (IOException e) {
                e.printStackTrace();
                onConnectionStateChanged(false);
            } finally {
                try {
                    if (mRead != null)
                        mRead.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        public boolean write(byte[] data) {
            try {
                mWrite.write(data);
                return true;
            } catch (IOException e) {
                e.printStackTrace();
                onConnectionStateChanged(false);
            }
            return false;
        }
    }

    private void LOG(String msg){
        Log.i(TAG, "LOG: " + msg);
        if(msg != null){
            LogUtils.e("SPP", "msg = "+ msg);
        }
    }
}
