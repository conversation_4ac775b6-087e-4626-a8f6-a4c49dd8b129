package com.bes_lib.bluetooth.scanner;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.Context;


import com.bes_lib.bluetooth.callback.ScanCallback;
import com.bes_lib.utils.LogUtils;

/**
 * 低功耗蓝牙(BLE)扫描器 - JellyBean版本实现
 *
 * 该类为Android 4.3-4.4 (API 18-20) 版本提供BLE扫描功能。
 * 使用了Android早期版本的BLE扫描API：BluetoothAdapter.LeScanCallback
 *
 * 主要特点：
 * 1. 使用BluetoothAdapter.startLeScan()和stopLeScan()方法
 * 2. 通过LeScanCallback接收扫描结果
 * 3. 适用于Android 4.3到4.4版本的设备
 * 4. 相比新版API功能较为简单，但兼容性好
 *
 * 注意：此实现主要用于向后兼容，在Android 5.0+设备上
 * 建议使用LeLollipopScanner以获得更好的性能和功能。
 *
 * Created by alloxuweibin on 2017/12/11.
 */
public class LeJBScanner extends BaseScanner {

    /**
     * 构造方法
     *
     * 创建适用于Android 4.3-4.4版本的BLE扫描器实例。
     * 继承自BaseScanner，具备基础的扫描器功能。
     *
     * @param context Android上下文对象，用于获取蓝牙适配器
     */
    public LeJBScanner(Context context) {
        super(context);
    }

    /**
     * 开始BLE设备扫描
     *
     * 使用Android 4.3-4.4版本的BLE扫描API开始扫描低功耗蓝牙设备。
     * 此方法会检查当前扫描状态，避免重复扫描，并根据扫描启动结果
     * 触发相应的回调事件。
     *
     * @param callback 扫描结果回调接口
     */
    @Override
    public void startScan(ScanCallback callback) {
        // 调用父类方法保存回调接口
        super.startScan(callback);

        // 检查是否已在扫描中，避免重复启动
        if (isScanning()) {
            return;
        }

        // 使用旧版BLE扫描API启动扫描
        if (getBluetoothAdapter().startLeScan(mLeScanCallback)) {
            // 扫描启动成功，触发扫描开始回调
            onScanStart();
        } else {
            // 扫描启动失败，直接触发扫描结束回调
            onScanFinish();
        }
    }

    /**
     * 停止BLE设备扫描
     *
     * 使用Android 4.3-4.4版本的BLE扫描API停止正在进行的扫描。
     * 此方法会检查当前扫描状态，只有在扫描中时才执行停止操作。
     */
    @Override
    public void stopScan() {
        // 检查是否正在扫描，未扫描时直接返回
        if (!isScanning())
            return;

        // 使用旧版BLE扫描API停止扫描
        getBluetoothAdapter().stopLeScan(mLeScanCallback);

        // 触发扫描结束回调
        onScanFinish();
    }

    /**
     * 关闭扫描器并释放资源
     *
     * 对于JellyBean版本的BLE扫描器，不需要特殊的资源清理操作，
     * 因为使用的是简单的回调机制，没有复杂的资源管理。
     */
    @Override
    public void close() {
        // JellyBean版本的BLE扫描器无需特殊清理操作
    }

    /**
     * BLE扫描结果回调实现
     *
     * 这是Android 4.3-4.4版本的BLE扫描回调接口实现。
     * 当系统发现BLE设备时，会调用此回调的onLeScan方法。
     * 此回调负责接收扫描结果并转发给上层应用。
     */
    private BluetoothAdapter.LeScanCallback mLeScanCallback = new BluetoothAdapter.LeScanCallback() {
        /**
         * BLE设备扫描结果回调方法
         *
         * 当扫描到BLE设备时，Android系统会调用此方法。
         * 此方法接收设备信息、信号强度和广告数据，
         * 并通过统一的回调机制转发给上层应用。
         *
         * @param device 发现的BLE设备对象
         * @param rssi 接收信号强度指示器(RSSI)
         * @param scanRecord BLE设备的广告数据记录
         */
        @Override
        public void onLeScan(BluetoothDevice device, int rssi, byte[] scanRecord) {
            // 检查设备对象是否有效，并记录扫描日志
            if(device != null){
                // 记录发现的设备信息，便于调试和问题排查
                LogUtils.e(TAG, "onLeScan " + device.toString());
            }

            // 调用父类的统一回调方法，将扫描结果转发给上层应用
            onFound(device, rssi, scanRecord);
        }
    };
}
