package com.bes_lib.bluetooth;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothManager;
import android.content.Context;
import android.os.Build;


import com.bes_lib.utils.ArrayUtil;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/**
 * 蓝牙辅助工具类
 *
 * 该工具类提供了蓝牙操作的各种辅助方法，主要功能包括：
 * 1. 蓝牙适配器和管理器的获取
 * 2. 蓝牙设备的创建和管理
 * 3. 蓝牙配对操作（创建、移除、取消等）
 * 4. 蓝牙广告数据解析
 * 5. 跨Android版本的兼容性处理
 *
 * 设计特点：
 * - 静态方法设计，方便全局调用
 * - 处理不同Android版本的API差异
 * - 使用反射机制调用隐藏API
 * - 提供完整的错误处理机制
 *
 * 兼容性：
 * - 支持Android 4.2+的蓝牙功能
 * - 自动适配不同版本的蓝牙API
 * - 处理厂商定制系统的兼容性问题
 *
 * Created by alloxuweibin on 2017/12/11.
 */
public class BtHelper {

    /**
     * 获取蓝牙管理器实例
     *
     * 从系统服务中获取BluetoothManager实例，用于管理蓝牙适配器。
     * 适用于Android 4.3+版本。
     *
     * @param context Android上下文对象
     * @return BluetoothManager实例，如果系统不支持则可能为null
     */
    public static BluetoothManager getBluetoothManager(Context context) {
        // 从系统服务中获取蓝牙管理器
        return (BluetoothManager) context.getSystemService(Context.BLUETOOTH_SERVICE);
    }

    /**
     * 获取蓝牙适配器实例（兼容不同Android版本）
     *
     * 根据Android版本选择合适的方式获取蓝牙适配器：
     * - Android 4.2及以下：使用BluetoothAdapter.getDefaultAdapter()
     * - Android 4.3及以上：通过BluetoothManager获取
     *
     * @param context Android上下文对象
     * @return BluetoothAdapter实例，如果设备不支持蓝牙则为null
     */
    public static BluetoothAdapter getBluetoothAdapter(Context context) {
        // 检查Android版本，选择合适的获取方式
        if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.JELLY_BEAN_MR1)
            // Android 4.2及以下版本使用旧方法
            return BluetoothAdapter.getDefaultAdapter();
        else {
            // Android 4.3及以上版本通过BluetoothManager获取
            return getBluetoothManager(context).getAdapter();
        }
    }

    /**
     * 根据MAC地址获取远程蓝牙设备对象
     *
     * 通过设备的MAC地址创建BluetoothDevice对象。
     * 注意：此方法不会进行实际的设备发现，只是创建设备对象。
     *
     * @param context Android上下文对象
     * @param address 蓝牙设备的MAC地址，格式如"00:11:22:33:44:55"
     * @return BluetoothDevice对象，如果地址格式错误则返回null
     */
    public static BluetoothDevice getRemoteDevice(Context context, String address) {
        try {
            // 通过蓝牙适配器根据MAC地址获取设备对象
            return getBluetoothAdapter(context).getRemoteDevice(address);
        } catch (IllegalArgumentException e) {
            // MAC地址格式错误时会抛出此异常
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 解析BLE广告数据中的厂商特定数据
     *
     * 从BLE设备的扫描记录中提取厂商特定数据（Manufacturer Specific Data）。
     * BLE广告数据采用TLV（Type-Length-Value）格式，此方法解析其中类型为0xFF的数据。
     *
     * 数据格式说明：
     * - 每个数据段：[长度][类型][数据]
     * - 长度：包含类型字段在内的总长度
     * - 类型：0xFF表示厂商特定数据
     * - 数据：前2字节为厂商ID（小端序），后续为厂商自定义数据
     *
     * @param scanRecord BLE设备的完整扫描记录数据
     * @return 厂商特定数据的字节数组（不包含厂商ID），如果未找到则返回null
     */
    public static byte[] parseManufacturerSpecificData(byte[] scanRecord) {
        if (scanRecord != null) {
            int currentPos = 0; // 当前解析位置

            // 遍历扫描记录中的所有数据段
            while (currentPos < scanRecord.length) {
                // 读取数据段长度（无符号字节）
                int length = scanRecord[currentPos++] & 0xFF;
                if (length == 0) {
                    // 长度为0表示数据结束
                    break;
                }

                // 计算实际数据长度（长度字段包含类型字段本身）
                int dataLength = length - 1;

                // 读取数据类型（无符号字节）
                int fieldType = scanRecord[currentPos++] & 0xFF;

                // 检查是否为厂商特定数据类型且数据长度足够
                if (fieldType == 0xFF && dataLength > 2) {
                    // 厂商特定数据的前2字节是厂商ID（小端序）
                    // 提取厂商ID之后的实际数据
                    byte[] manufacturerDataBytes = ArrayUtil.extractBytes(scanRecord, currentPos + 2,
                            dataLength - 2);
                    return manufacturerDataBytes;
                }

                // 跳过当前数据段，继续解析下一段
                currentPos += dataLength;
            }
        }
        return null;
    }

    /**
     * 创建蓝牙设备配对（兼容不同Android版本）
     *
     * 启动与指定蓝牙设备的配对过程。此方法会处理不同Android版本的API差异：
     * - Android 4.4+：使用公开的createBond()方法
     * - Android 4.4以下：使用反射调用隐藏的createBond()方法
     *
     * 配对过程说明：
     * - 配对是异步过程，此方法只是启动配对
     * - 配对结果需要通过广播接收器监听
     * - 可能需要用户确认PIN码或密钥
     *
     * @param btDevice 要配对的蓝牙设备对象
     * @return true表示配对请求发送成功，false表示失败
     */
    public static boolean createBond(BluetoothDevice btDevice) {
        // 检查Android版本，选择合适的配对方法
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            // Android 4.4+使用公开API
            return btDevice.createBond();
        } else {
            // Android 4.4以下使用反射调用隐藏API
            try {
                Class<?> cls = btDevice.getClass();
                Method method = cls.getMethod("createBond", new Class[0]);
                Boolean result = (Boolean) method.invoke(btDevice, new Object[0]);
                return result.booleanValue();
            } catch (NoSuchMethodException e) {
                // 方法不存在
                e.printStackTrace();
            } catch (IllegalAccessException e) {
                // 访问权限不足
                e.printStackTrace();
            } catch (InvocationTargetException e) {
                // 方法调用异常
                e.printStackTrace();
            }
        }
        return false;
    }

    /**
     * 移除蓝牙设备配对关系
     *
     * 删除与指定蓝牙设备的配对信息，使设备变为未配对状态。
     * 此方法使用反射调用Android隐藏API，因为removeBond()不是公开方法。
     *
     * 注意事项：
     * - 这是一个隐藏API，可能在某些设备上不可用
     * - 移除配对后，下次连接需要重新配对
     * - 操作成功后设备会从已配对设备列表中消失
     *
     * @param btDevice 要移除配对的蓝牙设备对象
     * @return true表示移除成功，false表示失败
     */
    public static boolean removeBond(BluetoothDevice btDevice) {
        Boolean returnValue = Boolean.FALSE;
        try {
            // 使用反射调用隐藏的removeBond方法
            returnValue = ((Boolean) btDevice.getClass().getMethod("removeBond", new Class[0]).invoke(btDevice, new Object[0])).booleanValue();
        } catch (IllegalAccessException e) {
            // 访问权限不足
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            // 方法调用异常
            e.printStackTrace();
        } catch (NoSuchMethodException e) {
            // 方法不存在（某些设备可能不支持）
            e.printStackTrace();
        }
        return returnValue.booleanValue();
    }

    /**
     * 设置蓝牙配对PIN码
     *
     * 在蓝牙配对过程中设置PIN码，用于自动完成配对认证。
     * 此方法使用反射调用Android隐藏API，主要用于经典蓝牙的PIN码配对。
     *
     * 使用场景：
     * - 自动化配对流程，无需用户手动输入PIN码
     * - 已知设备PIN码的情况下快速配对
     * - 批量设备配对的自动化处理
     *
     * 注意事项：
     * - 这是隐藏API，可能在某些设备上不可用
     * - PIN码会转换为UTF-8字节数组传递给系统
     * - 需要在配对过程中的适当时机调用
     *
     * @param btDevice 要设置PIN码的蓝牙设备对象
     * @param str PIN码字符串
     * @return true表示PIN码设置成功，false表示失败
     * @throws Exception 当反射调用失败时抛出异常
     */
    public static boolean setPin(BluetoothDevice btDevice, String str) throws Exception {
        Boolean returnValue = Boolean.FALSE;
        try {
            // 使用反射调用隐藏的setPin方法，传入UTF-8编码的PIN码字节数组
            returnValue = (Boolean) btDevice.getClass().getDeclaredMethod("setPin", new Class[]{byte[].class}).invoke(btDevice, new Object[]{str.getBytes("UTF-8")});
        } catch (SecurityException e) {
            // 安全权限不足
            e.printStackTrace();
        } catch (IllegalArgumentException e2) {
            // 参数错误
            e2.printStackTrace();
        } catch (Exception e3) {
            // 其他异常
            e3.printStackTrace();
        }
        return returnValue.booleanValue();
    }

    /**
     * 取消配对过程中的用户输入
     *
     * 取消当前正在进行的配对过程中的用户输入操作。
     * 此方法使用反射调用Android隐藏API，用于中断需要用户确认的配对流程。
     *
     * 使用场景：
     * - 配对超时时自动取消用户输入
     * - 用户主动取消配对操作
     * - 配对流程异常时的清理操作
     *
     * 注意事项：
     * - 这是隐藏API，可能在某些设备上不可用
     * - 只对正在进行的配对过程有效
     * - 取消后配对状态会回到未配对状态
     *
     * @param btDevice 要取消配对输入的蓝牙设备对象
     * @return true表示取消成功，false表示失败
     */
    public static boolean cancelPairingUserInput(BluetoothDevice btDevice) {
        Boolean returnValue = Boolean.FALSE;
        try {
            // 使用反射调用隐藏的cancelPairingUserInput方法
            ((Boolean) btDevice.getClass().getMethod("cancelPairingUserInput", new Class[0]).invoke(btDevice, new Object[0])).booleanValue();
        } catch (IllegalAccessException e) {
            // 访问权限不足
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            // 方法调用异常
            e.printStackTrace();
        } catch (NoSuchMethodException e) {
            // 方法不存在
            e.printStackTrace();
        }
        return returnValue.booleanValue();
    }

    /**
     * 取消蓝牙配对过程
     *
     * 完全取消当前正在进行的蓝牙配对过程。
     * 此方法使用反射调用Android隐藏API，用于强制中断配对流程。
     *
     * 与cancelPairingUserInput的区别：
     * - cancelPairingUserInput：只取消用户输入部分
     * - cancelBondProcess：取消整个配对过程
     *
     * 使用场景：
     * - 配对过程卡住时强制取消
     * - 应用退出时清理配对状态
     * - 配对异常时的恢复操作
     *
     * @param btDevice 要取消配对过程的蓝牙设备对象
     * @return true表示取消成功，false表示失败
     */
    public static boolean cancelBondProcess(BluetoothDevice btDevice) {
        Boolean returnValue = Boolean.FALSE;
        try {
            // 使用反射调用隐藏的cancelBondProcess方法
            returnValue = (Boolean) btDevice.getClass().getMethod("cancelBondProcess", new Class[0]).invoke(btDevice, new Object[0]);
        } catch (SecurityException e) {
            // 安全权限不足
            e.printStackTrace();
        } catch (IllegalArgumentException e2) {
            // 参数错误
            e2.printStackTrace();
        } catch (Exception e3) {
            // 其他异常
            e3.printStackTrace();
        }
        return returnValue.booleanValue();
    }
}
