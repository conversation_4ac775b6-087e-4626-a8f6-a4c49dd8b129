/*
 * Copyright (c) 2017 Baidu, Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.bes_lib.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;
import android.os.Build;

import java.util.Map;

/**
 * SharedPreferences统一管理工具类
 *
 * 该工具类提供了对Android SharedPreferences的统一封装和管理，主要功能包括：
 * 1. 多种数据类型的自动识别和存储（String、Integer、Boolean、Float、Long）
 * 2. 支持自定义SharedPreferences文件名
 * 3. 跨Android版本的兼容性处理（apply vs commit）
 * 4. 完整的CRUD操作（增删改查）
 * 5. 批量操作和数据清理功能
 *
 * 设计特点：
 * - 静态方法设计，方便全局调用
 * - 自动类型识别，无需手动指定数据类型
 * - 版本兼容性处理，优化性能
 * - 统一的错误处理和异常管理
 *
 * 使用场景：
 * - 应用配置信息的持久化存储
 * - 用户偏好设置的保存和读取
 * - 临时数据的本地缓存
 * - 应用状态的记录和恢复
 *
 * 注意事项：
 * - SharedPreferences是线程安全的
 * - 适合存储少量的键值对数据
 * - 不适合存储大量或复杂的数据结构
 *
 * <NAME_EMAIL> on 2017/5/24.
 */
public class PreferenceUtil {

    /**
     * 默认SharedPreferences文件名
     * 当不指定文件名时使用此默认值
     */
    private static final String APP_SHARD = "com.baidu.duer.dcs";

    /**
     * 保存数据到默认SharedPreferences文件
     *
     * 使用默认文件名保存键值对数据。此方法会自动识别数据类型
     * 并调用相应的SharedPreferences存储方法。
     *
     * 支持的数据类型：
     * - String: 字符串数据
     * - Integer: 整数数据
     * - Boolean: 布尔值数据
     * - Float: 浮点数数据
     * - Long: 长整型数据
     * - 其他类型: 自动转换为字符串存储
     *
     * @param context Android上下文对象
     * @param key 存储的键名
     * @param object 要存储的值，支持多种数据类型
     */
    public static void put(Context context, String key, Object object) {
        // 调用重载方法，使用默认文件名
        put(context, APP_SHARD, key, object);
    }

    /**
     * 保存数据到指定SharedPreferences文件
     *
     * 将键值对数据保存到指定名称的SharedPreferences文件中。
     * 此方法是数据存储的核心实现，支持多种数据类型的自动识别。
     *
     * @param context Android上下文对象
     * @param spName SharedPreferences文件名
     * @param key 存储的键名
     * @param object 要存储的值
     */
    public static void put(Context context, String spName, String key, Object object) {
        // 获取指定名称的SharedPreferences实例
        SharedPreferences sp = context.getSharedPreferences(spName,
                Context.MODE_PRIVATE);
        Editor editor = sp.edit();

        // 根据数据类型选择相应的存储方法
        if (object instanceof String) {
            // 字符串类型
            editor.putString(key, (String) object);
        } else if (object instanceof Integer) {
            // 整数类型
            editor.putInt(key, (Integer) object);
        } else if (object instanceof Boolean) {
            // 布尔类型
            editor.putBoolean(key, (Boolean) object);
        } else if (object instanceof Float) {
            // 浮点数类型
            editor.putFloat(key, (Float) object);
        } else if (object instanceof Long) {
            // 长整型
            editor.putLong(key, (Long) object);
        } else {
            // 其他类型转换为字符串存储
            editor.putString(key, object.toString());
        }

        // 提交更改，使用版本兼容的方式
        editSubmit(editor);
    }

    private static void editSubmit(Editor editor) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.GINGERBREAD) {
            editor.apply();
        } else {
            editor.commit();
        }
    }

    /**
     * 得到保存数据的方法，
     * 根据默认值得到保存的数据的具体类型，
     * 然后调用相对于的方法获取值
     *
     * @param context       上下文
     * @param key           key
     * @param defaultObject default-value
     */
    public static Object get(Context context, String key, Object defaultObject) {
        return get(context, APP_SHARD, key, defaultObject);
    }

    public static Object get(Context context, String spName, String key, Object defaultObject) {
        SharedPreferences sp = context.getSharedPreferences(spName,
                Context.MODE_PRIVATE);

        if (defaultObject instanceof String) {
            return sp.getString(key, (String) defaultObject);
        } else if (defaultObject instanceof Integer) {
            return sp.getInt(key, (Integer) defaultObject);
        } else if (defaultObject instanceof Boolean) {
            return sp.getBoolean(key, (Boolean) defaultObject);
        } else if (defaultObject instanceof Float) {
            return sp.getFloat(key, (Float) defaultObject);
        } else if (defaultObject instanceof Long) {
            return sp.getLong(key, (Long) defaultObject);
        }
        return null;
    }

    /**
     * 移除某个key值已经对应的值
     *
     * @param context 上下文
     * @param key     key
     */
    public static void remove(Context context, String key) {
        remove(context, APP_SHARD, key);
    }

    public static void remove(Context context, String spName, String key) {
        SharedPreferences sp = context.getSharedPreferences(spName,
                Context.MODE_PRIVATE);
        Editor editor = sp.edit();
        editor.remove(key);
        editSubmit(editor);
    }

    /**
     * 清除所有数据
     *
     * @param context 上下文
     */
    public static void clear(Context context) {
        clear(context, APP_SHARD);
    }

    public static void clear(Context context, String spName) {
        SharedPreferences sp = context.getSharedPreferences(spName, Context.MODE_PRIVATE);
        Editor editor = sp.edit();
        editor.clear();
        editSubmit(editor);
    }

    /**
     * 查询某个key是否已经存在
     *
     * @param context 上下文
     * @param key     key
     */
    public static boolean contains(Context context, String key) {
        return contains(context, APP_SHARD, key);
    }

    public static boolean contains(Context context, String spName, String key) {
        SharedPreferences sp = context.getSharedPreferences(spName, Context.MODE_PRIVATE);
        return sp.contains(key);
    }

    /**
     * 返回所有的键值对
     *
     * @param context 上下文
     */
    public static Map<String, ?> getAll(Context context) {
        return getAll(context, APP_SHARD);
    }

    public static Map<String, ?> getAll(Context context, String spName) {
        SharedPreferences sp = context.getSharedPreferences(spName,
                Context.MODE_PRIVATE);
        return sp.getAll();
    }
}
