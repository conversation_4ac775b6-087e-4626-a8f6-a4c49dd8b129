/*
 * Copyright (c) 2017 Baidu, Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.bes_lib.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;
import android.os.Build;

import java.util.Map;

/**
 * SharedPreferences统一管理工具类
 *
 * 该工具类提供了对Android SharedPreferences的统一封装和管理，主要功能包括：
 * 1. 多种数据类型的自动识别和存储（String、Integer、Boolean、Float、Long）
 * 2. 支持自定义SharedPreferences文件名
 * 3. 跨Android版本的兼容性处理（apply vs commit）
 * 4. 完整的CRUD操作（增删改查）
 * 5. 批量操作和数据清理功能
 *
 * 设计特点：
 * - 静态方法设计，方便全局调用
 * - 自动类型识别，无需手动指定数据类型
 * - 版本兼容性处理，优化性能
 * - 统一的错误处理和异常管理
 *
 * 使用场景：
 * - 应用配置信息的持久化存储
 * - 用户偏好设置的保存和读取
 * - 临时数据的本地缓存
 * - 应用状态的记录和恢复
 *
 * 注意事项：
 * - SharedPreferences是线程安全的
 * - 适合存储少量的键值对数据
 * - 不适合存储大量或复杂的数据结构
 *
 * <NAME_EMAIL> on 2017/5/24.
 */
public class PreferenceUtil {

    /**
     * 默认SharedPreferences文件名
     * 当不指定文件名时使用此默认值
     */
    private static final String APP_SHARD = "com.baidu.duer.dcs";

    /**
     * 保存数据到默认SharedPreferences文件
     *
     * 使用默认文件名保存键值对数据。此方法会自动识别数据类型
     * 并调用相应的SharedPreferences存储方法。
     *
     * 支持的数据类型：
     * - String: 字符串数据
     * - Integer: 整数数据
     * - Boolean: 布尔值数据
     * - Float: 浮点数数据
     * - Long: 长整型数据
     * - 其他类型: 自动转换为字符串存储
     *
     * @param context Android上下文对象
     * @param key 存储的键名
     * @param object 要存储的值，支持多种数据类型
     */
    public static void put(Context context, String key, Object object) {
        // 调用重载方法，使用默认文件名
        put(context, APP_SHARD, key, object);
    }

    /**
     * 保存数据到指定SharedPreferences文件
     *
     * 将键值对数据保存到指定名称的SharedPreferences文件中。
     * 此方法是数据存储的核心实现，支持多种数据类型的自动识别。
     *
     * @param context Android上下文对象
     * @param spName SharedPreferences文件名
     * @param key 存储的键名
     * @param object 要存储的值
     */
    public static void put(Context context, String spName, String key, Object object) {
        // 获取指定名称的SharedPreferences实例
        SharedPreferences sp = context.getSharedPreferences(spName,
                Context.MODE_PRIVATE);
        Editor editor = sp.edit();

        // 根据数据类型选择相应的存储方法
        if (object instanceof String) {
            // 字符串类型
            editor.putString(key, (String) object);
        } else if (object instanceof Integer) {
            // 整数类型
            editor.putInt(key, (Integer) object);
        } else if (object instanceof Boolean) {
            // 布尔类型
            editor.putBoolean(key, (Boolean) object);
        } else if (object instanceof Float) {
            // 浮点数类型
            editor.putFloat(key, (Float) object);
        } else if (object instanceof Long) {
            // 长整型
            editor.putLong(key, (Long) object);
        } else {
            // 其他类型转换为字符串存储
            editor.putString(key, object.toString());
        }

        // 提交更改，使用版本兼容的方式
        editSubmit(editor);
    }

    /**
     * 提交SharedPreferences编辑器的更改（版本兼容）
     *
     * 根据Android版本选择合适的提交方式：
     * - Android 2.3+：使用apply()方法（异步，性能更好）
     * - Android 2.3以下：使用commit()方法（同步）
     *
     * @param editor SharedPreferences编辑器对象
     */
    private static void editSubmit(Editor editor) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.GINGERBREAD) {
            // Android 2.3+使用apply()方法，异步执行，性能更好
            editor.apply();
        } else {
            // Android 2.3以下使用commit()方法，同步执行
            editor.commit();
        }
    }

    /**
     * 从默认SharedPreferences文件读取数据
     *
     * 使用默认文件名读取键值对数据。此方法会根据默认值的类型
     * 自动识别要读取的数据类型并调用相应的获取方法。
     *
     * 类型识别机制：
     * - 通过defaultObject的类型确定要读取的数据类型
     * - 如果键不存在，返回提供的默认值
     * - 支持String、Integer、Boolean、Float、Long类型
     *
     * @param context Android上下文对象
     * @param key 要读取的键名
     * @param defaultObject 默认值，同时用于确定数据类型
     * @return 读取到的值，如果键不存在则返回默认值
     */
    public static Object get(Context context, String key, Object defaultObject) {
        // 调用重载方法，使用默认文件名
        return get(context, APP_SHARD, key, defaultObject);
    }

    /**
     * 从指定SharedPreferences文件读取数据
     *
     * 从指定名称的SharedPreferences文件中读取键值对数据。
     * 此方法是数据读取的核心实现，支持多种数据类型的自动识别。
     *
     * @param context Android上下文对象
     * @param spName SharedPreferences文件名
     * @param key 要读取的键名
     * @param defaultObject 默认值，用于类型识别和键不存在时的返回值
     * @return 读取到的值，类型与defaultObject一致
     */
    public static Object get(Context context, String spName, String key, Object defaultObject) {
        // 获取指定名称的SharedPreferences实例
        SharedPreferences sp = context.getSharedPreferences(spName,
                Context.MODE_PRIVATE);

        // 根据默认值类型选择相应的读取方法
        if (defaultObject instanceof String) {
            // 读取字符串类型数据
            return sp.getString(key, (String) defaultObject);
        } else if (defaultObject instanceof Integer) {
            // 读取整数类型数据
            return sp.getInt(key, (Integer) defaultObject);
        } else if (defaultObject instanceof Boolean) {
            // 读取布尔类型数据
            return sp.getBoolean(key, (Boolean) defaultObject);
        } else if (defaultObject instanceof Float) {
            // 读取浮点数类型数据
            return sp.getFloat(key, (Float) defaultObject);
        } else if (defaultObject instanceof Long) {
            // 读取长整型数据
            return sp.getLong(key, (Long) defaultObject);
        }

        // 不支持的类型返回null
        return null;
    }

    /**
     * 从默认SharedPreferences文件移除指定键值对
     *
     * 删除默认SharedPreferences文件中指定键名的数据项。
     * 如果键不存在，此操作不会产生任何影响。
     *
     * @param context Android上下文对象
     * @param key 要移除的键名
     */
    public static void remove(Context context, String key) {
        // 调用重载方法，使用默认文件名
        remove(context, APP_SHARD, key);
    }

    /**
     * 从指定SharedPreferences文件移除指定键值对
     *
     * 删除指定SharedPreferences文件中指定键名的数据项。
     * 此操作会立即生效并持久化到存储中。
     *
     * @param context Android上下文对象
     * @param spName SharedPreferences文件名
     * @param key 要移除的键名
     */
    public static void remove(Context context, String spName, String key) {
        // 获取指定名称的SharedPreferences实例
        SharedPreferences sp = context.getSharedPreferences(spName,
                Context.MODE_PRIVATE);
        Editor editor = sp.edit();

        // 移除指定键的数据
        editor.remove(key);

        // 提交更改
        editSubmit(editor);
    }

    /**
     * 清除默认SharedPreferences文件中的所有数据
     *
     * 删除默认SharedPreferences文件中的所有键值对数据。
     * 此操作不可逆，请谨慎使用。
     *
     * @param context Android上下文对象
     */
    public static void clear(Context context) {
        // 调用重载方法，使用默认文件名
        clear(context, APP_SHARD);
    }

    /**
     * 清除指定SharedPreferences文件中的所有数据
     *
     * 删除指定SharedPreferences文件中的所有键值对数据。
     * 此操作会立即生效并持久化到存储中，且不可逆。
     *
     * @param context Android上下文对象
     * @param spName SharedPreferences文件名
     */
    public static void clear(Context context, String spName) {
        // 获取指定名称的SharedPreferences实例
        SharedPreferences sp = context.getSharedPreferences(spName, Context.MODE_PRIVATE);
        Editor editor = sp.edit();

        // 清除所有数据
        editor.clear();

        // 提交更改
        editSubmit(editor);
    }

    /**
     * 检查默认SharedPreferences文件中是否包含指定键
     *
     * 查询默认SharedPreferences文件中是否存在指定的键名。
     * 此方法只检查键是否存在，不关心对应的值。
     *
     * @param context Android上下文对象
     * @param key 要检查的键名
     * @return true表示键存在，false表示键不存在
     */
    public static boolean contains(Context context, String key) {
        // 调用重载方法，使用默认文件名
        return contains(context, APP_SHARD, key);
    }

    /**
     * 检查指定SharedPreferences文件中是否包含指定键
     *
     * 查询指定SharedPreferences文件中是否存在指定的键名。
     * 此方法只检查键是否存在，不关心对应的值。
     *
     * @param context Android上下文对象
     * @param spName SharedPreferences文件名
     * @param key 要检查的键名
     * @return true表示键存在，false表示键不存在
     */
    public static boolean contains(Context context, String spName, String key) {
        // 获取指定名称的SharedPreferences实例
        SharedPreferences sp = context.getSharedPreferences(spName, Context.MODE_PRIVATE);

        // 检查是否包含指定键
        return sp.contains(key);
    }

    /**
     * 获取默认SharedPreferences文件中的所有键值对
     *
     * 返回默认SharedPreferences文件中存储的所有键值对数据。
     * 返回的Map是只读的，修改它不会影响实际存储的数据。
     *
     * @param context Android上下文对象
     * @return 包含所有键值对的Map，键为String类型，值为Object类型
     */
    public static Map<String, ?> getAll(Context context) {
        // 调用重载方法，使用默认文件名
        return getAll(context, APP_SHARD);
    }

    /**
     * 获取指定SharedPreferences文件中的所有键值对
     *
     * 返回指定SharedPreferences文件中存储的所有键值对数据。
     * 返回的Map是只读的，修改它不会影响实际存储的数据。
     *
     * 使用场景：
     * - 数据备份和导出
     * - 调试和日志记录
     * - 批量数据处理
     *
     * @param context Android上下文对象
     * @param spName SharedPreferences文件名
     * @return 包含所有键值对的Map，键为String类型，值为Object类型
     */
    public static Map<String, ?> getAll(Context context, String spName) {
        // 获取指定名称的SharedPreferences实例
        SharedPreferences sp = context.getSharedPreferences(spName,
                Context.MODE_PRIVATE);

        // 返回所有键值对
        return sp.getAll();
    }
}
