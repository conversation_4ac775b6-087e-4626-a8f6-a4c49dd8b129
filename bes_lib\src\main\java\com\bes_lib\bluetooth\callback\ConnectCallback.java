package com.bes_lib.bluetooth.callback;


import java.util.UUID;

/**
 * 蓝牙连接状态和数据接收回调接口
 *
 * 该接口定义了蓝牙连接过程中的两个核心回调方法：
 * 1. 连接状态变化通知
 * 2. 数据接收通知
 *
 * 实现此接口的类可以监听蓝牙设备的连接状态变化，
 * 以及接收来自蓝牙设备的数据传输。
 *
 * Created by alloxuweibin on 2017/12/11.a3
 */
public interface ConnectCallback {

    /**
     * 蓝牙连接状态发生变化时的回调方法
     *
     * 当蓝牙设备连接或断开连接时，此方法会被调用。
     * 可以根据连接状态来更新UI界面或执行相应的业务逻辑。
     *
     * @param connected 连接状态标志
     *                 true: 设备已连接
     *                 false: 设备已断开连接
     */
    void onConnectionStateChanged(boolean connected);

    /**
     * 接收到蓝牙数据时的回调方法
     *
     * 当从蓝牙设备接收到数据时，此方法会被调用。
     * 数据通过指定的UUID特征值传输，可以根据UUID来区分不同的数据类型。
     *
     * @param uuid 数据传输使用的UUID特征值，用于标识数据类型或服务
     * @param data 接收到的原始字节数据，需要根据协议进行解析
     */
    void onReceive(UUID uuid , byte[] data);
}
