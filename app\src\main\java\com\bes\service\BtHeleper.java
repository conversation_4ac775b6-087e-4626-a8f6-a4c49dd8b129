package com.bes.service;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothManager;
import android.content.Context;
import android.os.Build;

/**
 * 蓝牙帮助工具类
 *
 * 功能描述：
 * - 提供蓝牙相关的工具方法
 * - 兼容不同Android版本的蓝牙API
 * - 简化蓝牙管理器和适配器的获取过程
 *
 * 设计目的：
 * - 封装Android版本差异，提供统一的蓝牙访问接口
 * - 简化蓝牙初始化代码，减少重复代码
 * - 提供静态方法，方便全局调用
 *
 * 版本兼容性：
 * - Android 4.2.2 (API 17) 及以下：使用BluetoothAdapter.getDefaultAdapter()
 * - Android 4.3 (API 18) 及以上：使用BluetoothManager.getAdapter()
 *
 * 使用场景：
 * - 蓝牙功能初始化
 * - 蓝牙状态检查
 * - 蓝牙设备扫描前的准备工作
 *
 * <AUTHOR>
 * @version 1.0
 */
public class BtHeleper {

    /**
     * 获取蓝牙管理器
     * 通过系统服务获取BluetoothManager实例
     *
     * @param context 应用程序上下文，用于获取系统服务
     * @return BluetoothManager实例，用于管理蓝牙相关操作
     */
    public static BluetoothManager getBluetoothManager(Context context) {
        // 通过系统服务获取蓝牙管理器
        return (BluetoothManager) context.getSystemService(Context.BLUETOOTH_SERVICE);
    }

    /**
     * 获取蓝牙适配器
     * 根据Android版本选择合适的方法获取BluetoothAdapter
     *
     * @param context 应用程序上下文，用于获取系统服务
     * @return BluetoothAdapter实例，用于蓝牙操作，如果设备不支持蓝牙则返回null
     */
    public static BluetoothAdapter getBluetoothAdapter(Context context) {
        // Android 4.2.2及以下版本使用旧的API
        if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.JELLY_BEAN_MR1)
            return BluetoothAdapter.getDefaultAdapter(); // 直接获取默认适配器
        else {
            // Android 4.3及以上版本使用新的API
            return getBluetoothManager(context).getAdapter(); // 通过管理器获取适配器
        }
    }
}
