package com.bes_lib.bluetooth.scanner;

import android.content.Context;
import android.os.Build;

/**
 * 低功耗蓝牙(BLE)扫描器兼容性工具类
 *
 * 该类提供了跨Android版本的BLE扫描器创建方法。
 * 由于Android在不同版本中使用了不同的BLE扫描API：
 * - Android 4.3-4.4 (API 18-20): 使用BluetoothAdapter.LeScanCallback
 * - Android 5.0+ (API 21+): 使用BluetoothLeScanner和ScanCallback
 *
 * 此工具类根据当前系统版本自动选择合适的扫描器实现，
 * 为上层应用提供统一的BLE扫描接口。
 *
 * Created by alloxuweibin on 2017/12/11.
 */
public class LeScannerCompat {

    /**
     * 根据Android系统版本获取合适的BLE扫描器实例
     *
     * 此方法会检查当前设备的Android版本，并返回对应的BLE扫描器实现：
     * - 对于Android 5.0以下版本，返回LeJBScanner（使用旧版API）
     * - 对于Android 5.0及以上版本，返回LeLollipopScanner（使用新版API）
     *
     * @param context Android上下文对象，用于初始化蓝牙适配器和相关服务
     * @return 适合当前系统版本的BLE扫描器实例
     */
    public static BtScanner getLeScanner(Context context) {
        // 检查Android版本，选择合适的BLE扫描器实现
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
            // Android 4.3-4.4版本使用旧版BLE扫描API
            return new LeJBScanner(context);
        } else {
            // Android 5.0+版本使用新版BLE扫描API
            return new LeLollipopScanner(context);
        }
    }
}
