package com.bes_lib.bluetooth;

import com.bes_lib.utils.LogUtils;
import com.bes_lib.utils.ArrayUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * SPP蓝牙消息协议解析助手类
 *
 * 该类专门用于处理SPP（Serial Port Profile）蓝牙通信中的消息协议解析，主要功能包括：
 * 1. 数据流的完整性检查和拼接
 * 2. 多种协议格式的识别和解析
 * 3. 语音数据流的特殊处理
 * 4. 控制指令的格式验证
 * 5. 数据包的分割和重组
 *
 * 协议支持：
 * - 语音流数据：0xFF 0xFF 开头的音频数据包
 * - 控制指令：0x00-0x07 开头的各种控制命令
 * - 数据流控制：开始/结束数据流的标志指令
 * - 配置指令：设备配置和状态查询指令
 *
 * 设计特点：
 * - 单例模式：确保全局唯一的消息处理实例
 * - 状态保持：维护数据解析的连续性
 * - 容错处理：处理不完整数据包的拼接
 * - 多协议支持：兼容多种消息格式
 *
 * 使用场景：
 * - SPP蓝牙设备的数据通信
 * - 音频数据流的实时处理
 * - 设备控制指令的解析
 * - 蓝牙协议栈的消息处理
 *
 * 注意事项：
 * - 线程安全：关键方法使用synchronized保护
 * - 内存管理：及时清理残留数据避免内存泄漏
 * - 协议兼容：支持多种版本的SPP协议格式
 *
 * Created by alloxuweibin on 2017/12/11.
 */
public class SppMessageHelper {

    /**
     * 日志标签，用于调试和错误追踪
     */
    String TAG = "SppMessageHelper";

    /**
     * 单例实例
     * 确保全局只有一个消息解析器实例
     */
    private static SppMessageHelper instant ;

    /**
     * 等待更多数据的缓存区
     *
     * 当接收到的数据不满足完整协议解析时，将残留数据保存在此缓存中，
     * 等待下次数据到达时进行拼接。蓝牙断开连接时需要清除此数据，
     * 避免不同连接会话之间的数据污染。
     */
    byte[] waitMoreDatas ;

    /**
     * 私有构造方法
     * 实现单例模式，防止外部直接创建实例
     */
    private SppMessageHelper(){};

    /**
     * 获取单例实例
     *
     * 使用懒加载方式创建单例实例，确保全局只有一个
     * SppMessageHelper对象用于消息解析。
     *
     * @return SppMessageHelper的单例实例
     */
    public static SppMessageHelper getInstant(){
        if(instant == null){
            instant = new SppMessageHelper();
        }
        return instant ;
    }

    /**
     * 检查数据完整性并返回解析后的数据包数组
     *
     * 这是消息解析的核心方法，负责检查接收到的数据是否满足协议长度要求。
     * 处理优先级：首先判断是否为语音流数据，然后处理其他控制指令。
     *
     * 处理流程：
     * 1. 将新数据与缓存数据拼接
     * 2. 循环检查是否有完整的数据包
     * 3. 提取完整的数据包并更新缓存
     * 4. 返回所有解析出的完整数据包
     *
     * @param data 新接收到的原始数据
     * @return 解析出的完整数据包数组，如果没有完整数据包则返回null
     */
    synchronized public byte[][] checkDataEnoughAndRetArray(byte[] data){
        // 创建数据包列表，用于存储解析出的完整数据包
        List<byte[]> mArrays = new ArrayList<byte[]>();

        // 检查输入数据的有效性
        if(data == null || data.length == 0){
            return  null ;
        }

        // 将新数据与缓存数据拼接，形成待解析的完整数据流
        waitMoreDatas = appendData(data);

        // 检查拼接后的数据是否有内容
        if(waitMoreDatas != null && waitMoreDatas.length > 0){
            // TODO: 这里应该添加循环解析逻辑
            // 当前代码段为空，可能需要补充数据包解析的具体实现
        }

        boolean isOut = false ;    // 退出迭代标志（当前未使用）

        // 检查是否解析出了完整的数据包
        if(mArrays != null && mArrays.size() > 0){
            LOG("--------- loop check total size is "+ mArrays.size());

            // 将List转换为数组返回
            byte[][] datas = new byte[mArrays.size()][];
            for (int i = 0 ; i < mArrays.size() ; i++){
                datas[i] = mArrays.get(i);
            }
            return datas ;
        }else{
            LOG("--------- loop check total size is zero");
        }
        return null ;
    }

    /**
     * 拼接数据流
     *
     * 将新接收到的数据与之前缓存的不完整数据进行拼接，
     * 形成连续的数据流用于协议解析。
     *
     * @param data 新接收到的数据
     * @return 拼接后的完整数据流
     */
    private byte[] appendData(byte[] data){
        if(waitMoreDatas != null){
            // 如果有缓存数据，则进行拼接
            byte[] dstData = new byte[waitMoreDatas.length + data.length];

            // 先复制缓存的数据
            System.arraycopy(waitMoreDatas , 0 , dstData , 0 , waitMoreDatas.length);

            // 再复制新接收的数据
            System.arraycopy(data , 0 , dstData , waitMoreDatas.length , data.length);

            return dstData ;
        }else{
            // 如果没有缓存数据，直接返回新数据
            return data ;
        }
    }

    /**
     * 检查数据长度是否足够进行有效性判断
     *
     * 只有当数据长度大于1字节时才能进行协议头的判断，
     * 如果长度不足则需要等待更多数据到达。
     *
     * @param datas 要检查的数据
     * @return true表示数据长度足够检查，false表示需要等待更多数据
     */
    private boolean isEnoughDataToCheck(byte[] datas){
        if(datas != null && datas.length > 1){
            return  true ;
        }
        return  false ;
    }

    /**
     * 判断是否为语音流数据
     *
     * 语音流数据的协议标识为0xFF 0xFF开头。
     * 这种数据包通常包含音频PCM数据，需要特殊处理。
     *
     * @param data 要检查的数据
     * @return true表示是语音流数据，false表示不是
     */
    private boolean isVoiceData(byte[] data){
        if(data != null && data.length > 1 && (data[0]&0xff) == 0XFF && (data[1]&0xff)==0xff ){
            return true ;
        }
        return  false ;
    }







    /**
     * 检查控制指令是否完整且格式正确
     *
     * 根据协议头字节判断不同类型的控制指令，并检查数据长度是否满足要求。
     * 支持多种控制指令格式的验证。
     *
     * @param datas 要检查的数据
     * @return true表示指令完整且格式正确，false表示不完整或格式错误
     */
    private boolean isEnoughCmdAndRight(byte[] datas){
        // 检查数据有效性
        if(datas == null && datas.length <= 1){
            return false;
        }

        // 根据协议头字节判断不同的指令类型
        if((datas[0]&0xff) == 0x07 && datas.length >= 0){
            return true ;
        }else if((datas[0]&0xff) == 0x04 && datas.length >= 5){
            return true ;
        }else if((datas[0]&0xff) == 0x05 && datas.length >= 0){
            return true ;
        }else if((datas[0]&0xff) == 0x01 && datas.length >= 0){
            return  true ;
        }else if((datas[0]&0xff) == 0x03 && datas.length >= 0){
            return  true ;
        }else if((datas[0]&0xff) == 0x00 && datas.length >= 0){
            return  true ;
        }
        return false ;
    }

    /**
     * 获取控制指令数据
     *
     * 根据协议头字节识别不同类型的控制指令，并提取对应长度的完整数据包。
     * 支持多种SPP协议格式：
     *
     * 协议格式说明：
     * - 0x07: 5字节指令 (07,80,01,00,01)
     * - 0x04: 5字节指令 (04,80,01,00,01) - 准备开始数据流标志
     * - 0x05: 4字节指令 (05,80,00,00) - 准备结束数据流标志
     * - 0x01: 12字节指令 (01,80,08,00,00,00,00,00,00,00,00,00) - 开始数据流标志
     * - 0x03: 16字节指令 (03,80,0c,00,00,00,00,00,00,00,00,00,00,00,00,00) - 结束数据流标志
     * - 0x00: 变长指令 - 长度由第3、4字节决定，用于配置和查询操作
     *
     * @param datas 包含控制指令的数据
     * @return 提取的完整控制指令数据，如果格式不正确则返回null
     */
    private byte[] getCmdData(byte[] datas){
        int len = 0 ;

        if((datas[0]&0xff) == 0x07 && datas.length >= 5){
            // 0x07指令：5字节固定长度 (07,80,01,00,01)
            LOG("getCmdData start byte is 0x07");
            len = 5  ;
        }else if((datas[0]&0xff) == 0x04 && datas.length >= 5){
            // 0x04指令：5字节固定长度 (04,80,01,00,01) - 准备开始数据流标志
            LOG("getCmdData start byte is 0x04");
            len = 5  ;
        }else if((datas[0]&0xff) == 0x05 && datas.length >= 4){
            // 0x05指令：4字节固定长度 (05,80,00,00) - 准备结束数据流标志
            LOG("getCmdData  start byte is 0x05");
            len = 4  ;
        }else if((datas[0]&0xff) == 0x01 && datas.length >= 12){
            // 0x01指令：12字节固定长度 (01,80,08,00,00,00,00,00,00,00,00,00) - 开始数据流标志
            LOG("getCmdData  start byte is 0x01");
            len = 12  ;
        }else if((datas[0]&0xff) == 0x03 && datas.length >= 16){
            // 0x03指令：16字节固定长度 (03,80,0c,00,00,00,00,00,00,00,00,00,00,00,00,00) - 结束数据流标志
            LOG("getCmdData  start byte is 0x03");
            len = 16  ;
        }else if((datas[0]&0xff) == 0x00 && datas.length >= 10){
            // 0x00指令：变长指令，长度由第3、4字节决定
            // 示例格式：
            // 00,80,07,00,04,80,00,00,01,00,02,
            // 00,80,06,00,05,80,00,00,00,00,
            // 00,80,0e,00,0d,80,00,00,08,00,03,18,a0,02,00,0c,01,00, //获取配置文件
            // 00,80,2f,00,0d,80,00,00,29,00,00,03,00,c0,00,00,06,01,00,01,00,00,00,00,0c,02,00,10,00,08,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,
            LOG("getCmdData  start byte is 0x00");

            if(datas.length > 4){
                // 长度计算：第3字节(低位) + 第4字节(高位) + 4字节头部
                len = (datas[2] & 0xff) + (datas[3] << 8) + 4;
            }else{
                return  null;
            }

        }else{
            // 未知的协议头，记录错误日志
            LOG("getCmdData and i donot known why ret null \n "+ ArrayUtil.toHex(datas));
            return  null ;
        }

        // 提取指定长度的数据并更新缓存
        return  getDataAndLeave(datas , len) ;
    }

    /**
     * 提取指定长度的数据并处理剩余数据
     *
     * 从数据流中提取指定长度的完整数据包，并将剩余数据保存到缓存中
     * 等待下次处理。这是数据包分割的核心逻辑。
     *
     * @param datas 原始数据流
     * @param len 要提取的数据长度
     * @return 提取的数据包，如果数据不足则返回null
     */
    private byte[] getDataAndLeave(byte[] datas , int len) {
        // 检查数据长度是否足够
        if(datas == null || datas.length < len){
            LOG("getDataAndLeave datas == null || datas.length < len len = "+ len);
            waitMoreDatas = null ;
            return  null ;
        }

        // 提取指定长度的数据
        byte[] dstDatas = new byte[len];
        System.arraycopy(datas , 0 , dstDatas , 0 , len);

        if(datas.length == len){
            // 数据刚好用完，清空缓存
            LOG("getDataAndLeave and waitMoreDatas is clear");
            waitMoreDatas = null ;
        }else{
            // 还有剩余数据，保存到缓存中等待下次处理
            int leaveDataLen = datas.length  - len ;
            byte[] leaveData = new byte[leaveDataLen];
            System.arraycopy(datas ,len , leaveData , 0 , leaveDataLen  );
            waitMoreDatas = leaveData ;
            LOG("getDataAndLeave and waitMoreDatas has leave some datas \n"+ ArrayUtil.toHex(waitMoreDatas));
        }
        return  dstDatas ;
    }

    /**
     * 清空SPP数据缓存
     *
     * 清除所有缓存的不完整数据，通常在蓝牙连接断开时调用，
     * 避免不同连接会话之间的数据污染。
     */
    public void clearSppData(){
        if(waitMoreDatas != null){
            waitMoreDatas = null ;
        }
    }

    /**
     * 内部日志输出方法
     *
     * 统一的日志输出接口，用于调试和错误追踪。
     * 所有日志都会标记为ERROR级别以确保显示。
     *
     * @param msg 要输出的日志消息
     */
    private void LOG(String msg){
        if(msg != null){
            LogUtils.e(TAG , msg);
        }
    }

}
