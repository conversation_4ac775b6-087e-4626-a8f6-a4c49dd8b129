package com.bes_lib.utils;

import android.os.Environment;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * 文件操作工具类
 *
 * 该工具类专门用于BES蓝牙库的文件管理操作，主要功能包括：
 * 1. 应用数据目录的创建和管理
 * 2. 日志文件的写入和大小控制
 * 3. PCM音频文件的保存和管理
 * 4. 文件存在性检查和删除操作
 * 5. OTA升级相关文件管理
 *
 * 目录结构：
 * - 根目录：外部存储根目录
 * - BES/：应用主目录
 * - BES/LogData/：日志数据目录
 * - BES/Pcm/：PCM音频文件目录
 *
 * 设计特点：
 * - 自动创建必要的目录结构
 * - 文件大小限制和自动清理
 * - 线程安全的目录创建
 * - 完整的异常处理
 *
 * 使用场景：
 * - 蓝牙通信日志记录
 * - 音频数据的本地保存
 * - OTA升级文件管理
 * - 应用数据的持久化存储
 */
public class FileUtils
{
	/**
	 * 外部存储根路径
	 * 指向设备的外部存储根目录（通常是/sdcard/）
	 */
	private static String mPath	= Environment.getExternalStorageDirectory() + "/";

	/**
	 * 应用文件夹名称
	 * 在外部存储中创建的应用专用目录名
	 */
	private static String FILE_FOLD = "bes";

	/**
	 * SPP通信相关文件名前缀
	 * 用于标识SPP蓝牙通信相关的文件
	 */
	public static String SPP_FILE_NAME = "spp";

	/**
	 * BLE通信相关文件名前缀
	 * 用于标识BLE蓝牙通信相关的文件
	 */
	public static String BLE_FILE_NAME = "ble";

	/**
	 * OTA升级统计文件名
	 * 用于记录OTA升级过程的统计信息
	 */
	public static String OTA_STATIC = "ota_static";//ota 统计记表

	/**
	 * USB OTA升级文件名
	 * 用于USB方式的OTA升级文件
	 */
	public static String USB_OTA_FILE = "usb_ota.txt";


	/**
	 * 检查指定路径是否存在，不存在则创建目录
	 *
	 * 此方法用于确保指定的目录路径存在，如果不存在会自动创建。
	 * 使用同步锁确保在多线程环境下的安全性。
	 *
	 * @param path 要检查和创建的目录路径
	 */
	public static void isExist(String path)
	{
		File file = new File(path);
		// 判断文件夹是否存在,如果不存在则创建文件夹
		if (!file.exists())
		{
			// 使用同步锁确保线程安全
			synchronized (FileUtils.class)
			{
				// 创建目录及其父目录
				file.mkdirs();
			}
		}
	}

	/**
	 * 检查指定路径的文件是否存在
	 *
	 * 简单的文件存在性检查方法，不会创建文件或目录。
	 *
	 * @param path 要检查的文件路径
	 * @return true表示文件存在，false表示文件不存在
	 */
	public static boolean isFileExist(String path)
	{
		File file = new File(path);
		return file.exists();
	}

	/**
	 * 获取应用数据存储根路径
	 *
	 * 返回应用在外部存储中的根目录路径，并确保该目录存在。
	 * 所有应用相关的文件都应该保存在此路径下的子目录中。
	 *
	 * @return 应用数据存储的根路径字符串
	 */
	public static String getFolderPath()
	{
		// 使用外部存储根路径作为基础路径
		String pathString = mPath;

		// 确保路径存在
		isExist(pathString);

		// 添加路径分隔符
		pathString += "/";
		return pathString;
	}


	/**
	 * 删除应用数据目录中的指定文件
	 *
	 * 在应用的数据存储目录中删除指定名称的文件。
	 * 此方法会检查文件是否存在，只有存在时才执行删除操作。
	 *
	 * @param fileName 要删除的文件名（不包含路径）
	 *                 例如：APK文件名、软件包名等
	 */
	public static void deleteFile(String fileName)
	{
		// 构建完整的文件路径
		File file = new File(getFolderPath() + fileName);

		// 检查文件是否存在，存在则删除
		if (file.exists())
		{
			file.delete();
		}
	}


	/**
	 * 写入日志数据到文件并自动清理大文件
	 *
	 * 将指定内容写入到BES/LogData/目录下的日志文件中。
	 * 具有自动文件大小管理功能，当文件超过80MB时会自动删除。
	 *
	 * 功能特点：
	 * - 自动创建必要的目录结构
	 * - 文件不存在时自动创建
	 * - 追加模式写入，保留历史数据
	 * - 自动监控文件大小，防止占用过多存储空间
	 * - 每行数据自动添加换行符
	 *
	 * @param filename 日志文件名（不包含路径）
	 * @param context 要写入的日志内容
	 */
	public static void writeTOfileAndActiveClear(String filename, String context)
	{
		// 构建BES主目录路径并确保存在
		String path = getFolderPath()+"BES/";
		isExist(path);

		// 构建LogData子目录路径并确保存在
		path = path+"LogData/";
		isExist(path);

		// 创建目标文件对象
		File file = new File(path + filename);
		try
		{
			// 如果文件不存在则创建新文件
			if (!file.exists())
			{
				file.createNewFile();
			}

			// 检查文件大小，实现自动清理机制
			FileInputStream fis = new FileInputStream(file);
			long size = fis.available(); // 获取文件大小（字节）
			fis.close();

			/**
			 * 当文件大小大于80MB时，主动删除文件
			 * 这样可以防止日志文件占用过多存储空间
			 */
			if (size >= 80000000) // 80MB = 80 * 1024 * 1024 ≈ 80000000字节
			{
				file.delete();
				return; // 删除后直接返回，不写入新数据
			}

			// 以追加模式打开文件输出流
			FileOutputStream stream = new FileOutputStream(file, true);

			// 在内容末尾添加换行符，确保每条日志占一行
			String temp = context + "\n";
			byte[] buf = temp.getBytes();

			// 写入数据并关闭流
			stream.write(buf);
			stream.close();

		}
		catch (IOException e)
		{
			// 捕获并打印IO异常，便于调试
			e.printStackTrace();
		}
	}

	/**
	 * 获取PCM音频文件的完整路径
	 *
	 * 根据文件名构建PCM音频文件的完整存储路径。
	 * PCM文件保存在BES/Pcm/目录下，自动添加.pcm扩展名。
	 *
	 * 目录结构：外部存储根目录/BES/Pcm/文件名.pcm
	 *
	 * @param fileName PCM文件名（不包含扩展名）
	 * @return PCM文件的完整路径字符串
	 */
	public static String getPcmFilePath (String fileName){
		// 构建BES主目录路径并确保存在
		String path = getFolderPath()+"BES/";
		isExist(path);

		// 构建Pcm子目录路径并确保存在
		path = path+"Pcm/";
		isExist(path);

		// 返回完整的PCM文件路径，自动添加.pcm扩展名
		return path + fileName + ".pcm";
	}

	/**
	 * 删除PCM音频文件
	 *
	 * 此方法当前被注释掉，预留用于删除PCM文件的功能。
	 * 可以根据需要取消注释并传入具体的文件名参数。
	 */
	public static void deletePcmFile(){
		// 预留的PCM文件删除功能，当前被注释
//		File file = new File(getPcmFilePath());
//		if(file.exists()){
//			file.delete();
//		}
	}

	/**
	 * 保存PCM音频数据到文件
	 *
	 * 将PCM音频数据追加保存到指定的文件中。
	 * 具有自动文件大小管理功能，当文件超过20MB时会自动删除。
	 *
	 * 功能特点：
	 * - 自动创建PCM文件存储目录
	 * - 文件不存在时自动创建
	 * - 追加模式写入，支持连续录音
	 * - 自动监控文件大小，防止占用过多存储空间
	 * - 专门针对音频数据优化的大小限制（20MB）
	 *
	 * 使用场景：
	 * - 蓝牙音频数据的实时保存
	 * - 通话录音功能
	 * - 音频调试和分析
	 *
	 * @param datas PCM音频数据的字节数组
	 * @param fileName PCM文件名（不包含扩展名和路径）
	 */
	public static void savePcmFile(byte[] datas , String fileName)
	{
		// 获取PCM文件的完整路径
		File file = new File(getPcmFilePath(fileName));
		try
		{
			// 如果文件不存在则创建新文件
			if (!file.exists())
			{
				file.createNewFile();
			}

			// 检查文件大小，实现自动清理机制
			FileInputStream fis = new FileInputStream(file);
			long size = fis.available(); // 获取文件大小（字节）
			fis.close();

			/**
			 * 当文件大小大于20MB时，主动删除文件
			 * PCM音频文件通常较大，设置较小的限制以节省存储空间
			 */
			if (size >= 20000000) // 20MB = 20 * 1024 * 1024 ≈ 20000000字节
			{
				file.delete();
				return; // 删除后直接返回，不写入新数据
			}

			// 以追加模式打开文件输出流，支持连续录音
			FileOutputStream stream = new FileOutputStream(file, true);

			// 直接写入PCM音频数据
			stream.write(datas);
			stream.close();

		}
		catch (IOException e)
		{
			// 捕获并打印IO异常，便于调试音频保存问题
			e.printStackTrace();
		}
	}

}
