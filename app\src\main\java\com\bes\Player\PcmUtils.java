package com.bes.Player;

import android.content.Context;
import android.media.AudioFormat;
import android.media.AudioManager;
import android.media.AudioRecord;
import android.media.AudioTrack;
import android.util.Log;

import com.bes_lib.utils.ArrayUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.LinkedBlockingDeque;

/**
 * PCM音频工具类
 *
 * 功能描述：
 * - 提供PCM音频数据的播放功能
 * - 管理音频数据的缓存和播放队列
 * - 支持单声道和立体声音频播放
 * - 基于AudioTrack实现低延迟音频播放
 *
 * 设计特点：
 * - 单例模式，确保全局唯一的音频播放管理器
 * - 异步播放，使用独立线程处理音频播放
 * - 数据缓存，支持音频数据的批量处理
 * - 线程安全，支持多线程环境下的音频操作
 *
 * 音频参数：
 * - 采样率：48000 Hz
 * - 编码格式：PCM 16位
 * - 声道配置：支持单声道和立体声
 * - 播放模式：流式播放（STREAM模式）
 *
 * 使用场景：
 * - 蓝牙音频数据的实时播放
 * - PCM格式音频文件的播放
 * - 音频数据的缓存和管理
 * - 低延迟音频播放需求
 *
 * 性能特点：
 * - 基于AudioTrack的硬件加速播放
 * - 支持音频数据的预加载和缓存
 * - 异步处理，不阻塞主线程
 * - 自动资源管理和释放
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2017/12/9
 */
public class PcmUtils {
    /** 日志标签 */
    private String TAG = "PcmUtils";

    /**
     * 单例实例
     * 确保全局只有一个PcmUtils实例
     */
    private  static PcmUtils instant ;

    /**
     * 音频数据缓存队列
     * 使用ArrayList存储待播放的PCM音频数据
     * 每个元素为一个字节数组，代表一段音频数据
     */
    List<byte[]> linkedBlockingDeque = new ArrayList<>();

    /**
     * 私有构造方法
     * 防止外部直接创建实例，确保单例模式的实现
     */
    private PcmUtils(){
        // 私有构造方法，单例模式
    }

    /**
     * 获取PcmUtils的单例实例
     * 使用懒加载模式，在首次调用时创建实例
     *
     * @return PcmUtils的唯一实例
     */
    public static  PcmUtils getInstant(){
        if(instant == null) { // 检查实例是否已创建
            instant = new  PcmUtils(); // 创建新实例
        }
        return  instant ; // 返回单例实例
    }


    // ==================== 音频数据管理方法 ====================

    /**
     * 添加音频数据到播放队列
     * 将PCM音频数据添加到缓存队列中，等待播放
     *
     * @param data PCM音频数据字节数组，不能为null
     */
    public void addData(byte[] data) {
        if(data != null) { // 检查数据是否有效
            // 注释掉的代码：原本用于数据分片处理
//            int i = 0;
//            int l = data.length / 160; // 按160字节分片
//
//            while (l > 1024 * 1024 * 8) { // 限制分片大小
//                l = l / 160;
//            }
//            while (i < data.length) { // 循环分片
//                byte[] pData = new byte[l];
//                System.arraycopy(data, i, pData, 0, l);
//                i += l;
                linkedBlockingDeque.add(data); // 直接添加完整数据到队列

//                checkSavePcmLoopThreadAlive(); // 检查处理线程
//
//            }
            checkSavePcmLoopThreadAlive(); // 确保处理线程运行
            return;
        }
//        linkedBlockingDeque.add(data); // 注释掉的备用添加逻辑
    }

    /**
     * 检查PCM文件处理线程是否存活
     * 如果线程不存在、已死亡或被中断，则创建新的处理线程
     * 注意：当前实现中线程的run方法为空，可能是预留的扩展点
     */
    private void checkSavePcmLoopThreadAlive(){
        // 检查线程状态：不存在、已死亡或被中断
        if(pcmFileThread == null || !pcmFileThread.isAlive() || pcmFileThread.isInterrupted()){
            pcmFileThread = new Thread(new Runnable() {
                @Override
                public void run() {
                    // 空实现，可能是预留的文件处理逻辑
                }
            });
            pcmFileThread.start(); // 启动新线程
        }
    }

    /**
     * 重置PCM文件状态
     * 停止当前播放并清理相关资源
     */
    public void resetPcmFile(){
        Log.i(TAG , "resetPcmFile()"); // 记录重置操作
        stopPlay(); // 停止播放
    }

    // ==================== 音频播放控制方法 ====================

    /**
     * 开始播放音频
     * 创建新的音频播放器并开始播放队列中的音频数据
     *
     * @param type 音频类型，0表示单声道，1表示立体声
     * @return 总是返回true，表示播放启动成功
     */
    public boolean play(int type){
        Log.i(TAG , "play()"); // 记录播放开始
        if(pcmPlayer != null){ // 如果已有播放器在运行
            Log.i(TAG , "pcmPlayer != null so cancelPlay"); // 记录取消操作
            pcmPlayer.cancelPlay(); // 取消当前播放
        }
        pcmPlayer = new PcmPlayer(type); // 创建新的播放器
        pcmPlayer.start(); // 启动播放线程
        return  true ; // 返回成功标志
    }

    /**
     * 停止音频播放
     * 取消当前的音频播放并释放相关资源
     */
    public void stopPlay(){
        Log.i(TAG , "stopPlay()"); // 记录停止操作
        if(pcmPlayer != null){ // 如果播放器存在
            pcmPlayer.cancelPlay(); // 取消播放
        }
    }



    // ==================== 成员变量 ====================

    /**
     * PCM文件处理线程
     * 用于处理PCM文件相关操作的后台线程
     * 当前实现为空线程，可能是预留的扩展功能
     */
    Thread pcmFileThread = new Thread();

    /**
     * PCM音频播放器
     * 负责实际的音频播放操作
     * 默认初始化为单声道播放器（type=0）
     */
    PcmPlayer pcmPlayer = new PcmPlayer(0) ;

    // ==================== 内部类：PCM音频播放器 ====================

    /**
     * PCM音频播放器内部类
     * 继承Thread类，在独立线程中处理音频播放
     *
     * 功能特点：
     * - 基于AudioTrack实现PCM音频播放
     * - 支持单声道和立体声播放
     * - 异步播放，不阻塞主线程
     * - 自动资源管理和释放
     *
     * 音频配置：
     * - 采样率：48000 Hz
     * - 编码：PCM 16位
     * - 缓冲区：动态计算，基于最小缓冲区大小的4倍
     * - 播放模式：流式播放
     */
    class PcmPlayer extends Thread {
        /** 播放状态标志 */
        boolean isPlay = false;

        /** Android音频播放轨道对象 */
        AudioTrack audioRtack;

        /**
         * PCM播放器构造方法
         * 初始化AudioTrack并配置音频参数
         *
         * @param type 音频类型，0表示单声道，1表示立体声
         */
        public PcmPlayer(int type) {
            Log.i(TAG, "PcmPlayer: ++++++++" + type); // 记录播放器创建

            // 计算最小缓冲区大小
            int bufferSize = AudioRecord.getMinBufferSize(
                48000, // 采样率：48kHz
                type == 0 ? AudioFormat.CHANNEL_CONFIGURATION_MONO : AudioFormat.CHANNEL_IN_STEREO, // 声道配置
                AudioFormat.ENCODING_PCM_16BIT // 编码格式：16位PCM
            );
            Log.i(TAG, "frame size = " + bufferSize * 4); // 记录缓冲区大小

            // 创建AudioTrack实例
            audioRtack = new AudioTrack(
                AudioManager.STREAM_MUSIC, // 音频流类型：音乐
                48000, // 采样率：48kHz
                type == 0 ? AudioFormat.CHANNEL_CONFIGURATION_MONO : AudioFormat.CHANNEL_IN_STEREO, // 声道配置
                AudioFormat.ENCODING_PCM_16BIT, // 编码格式
                bufferSize * 4, // 缓冲区大小：最小缓冲区的4倍
                AudioTrack.MODE_STREAM // 播放模式：流式播放
            );
            audioRtack.play(); // 开始播放（准备接收数据）
        }

        /**
         * 取消播放并释放资源
         * 停止音频播放并释放AudioTrack资源
         */
        public void cancelPlay() {
            isPlay = false; // 设置停止标志
            synchronized (audioRtack) { // 同步操作，确保线程安全
                if (audioRtack != null && audioRtack.getPlayState() == AudioTrack.PLAYSTATE_PLAYING) {
                    audioRtack.stop(); // 停止音频播放
                    audioRtack.release(); // 释放AudioTrack资源
                }
            }
        }

        /**
         * 线程运行方法
         * 在独立线程中播放音频数据队列中的所有PCM数据
         *
         * 播放流程：
         * 1. 设置播放状态并记录开始日志
         * 2. 遍历音频数据队列，逐个播放音频数据
         * 3. 对于非第一个数据，先清空前一个数据（优化内存）
         * 4. 将PCM数据写入AudioTrack进行播放
         * 5. 播放完成后清理资源和队列
         *
         * 特殊处理：
         * - 使用for循环而非while循环，一次性播放所有队列数据
         * - 对已播放的数据进行内存优化处理
         * - 异常处理确保单个数据异常不影响整体播放
         * - 播放完成后自动清理队列和释放资源
         */
        @Override
        public void run() {
            super.run(); // 调用父类方法
            Log.i(TAG, "PcmPlayer() run++++"); // 记录播放开始
            isPlay = true; // 设置播放状态

            // 注释掉的代码：原本用于调试队列状态
//            if (linkedDeque != null) {
//                Log.i(TAG, "linkedDeque size++++ = " + linkedDeque.size());
//            }

            if (isPlay) { // 检查播放状态
                Log.i(TAG, "run: ---" + linkedBlockingDeque.size()); // 记录队列大小

                // 遍历播放队列中的所有音频数据
                for (int i = 0; i < linkedBlockingDeque.size(); i++) {
                    if (i > 0) { // 对于非第一个数据
                        linkedBlockingDeque.remove(i - 1); // 移除前一个数据
                        linkedBlockingDeque.add(i - 1, new byte[0]); // 添加空数据占位（内存优化）
                    }
                    byte[] data = linkedBlockingDeque.get(i); // 获取当前音频数据
                    try {
                        Log.i(TAG, "PcmPlayer() run data +++++ " + data.length); // 记录数据长度
                        audioRtack.write(data, 0, data.length); // 将PCM数据写入AudioTrack播放
                    } catch (Exception e) {
                        e.printStackTrace(); // 处理播放异常，确保不中断整体播放
                    }
                }
            }

            Log.i(TAG, "PcmPlayer() run done++++++"); // 记录播放完成
            isPlay = false; // 重置播放状态

            // 同步清理资源
            synchronized (audioRtack) {
                linkedBlockingDeque = new ArrayList<>(); // 清空音频数据队列

                if (audioRtack != null && audioRtack.getPlayState() == AudioTrack.PLAYSTATE_PLAYING) {
                    audioRtack.stop(); // 停止音频播放
                    audioRtack.release(); // 释放AudioTrack资源
                }
            }

        }
    }

}
