package com.bes_lib.utils;

import android.os.Build;
import android.os.Handler;
import android.os.Message;

import java.sql.Time;

/**
 * 超时限制管理工具类
 *
 * 该类提供了基于时间的超时控制功能，主要用于：
 * 1. 操作超时检测和处理
 * 2. 异步任务的时间限制
 * 3. 蓝牙连接等耗时操作的超时管理
 * 4. 用户交互的响应时间控制
 *
 * 设计模式：
 * - 单例模式：确保全局只有一个超时管理实例
 * - 观察者模式：通过回调接口通知超时事件
 *
 * 工作原理：
 * - 记录操作开始时间
 * - 使用Handler延时发送超时消息
 * - 提供同步和异步两种超时检测方式
 *
 * 使用场景：
 * - 蓝牙连接超时控制
 * - 网络请求超时处理
 * - 用户操作响应时间限制
 *
 * Created by alloxuweibin on 2017/12/10.
 */
public class TimeLimit {

    /**
     * 超时开始时间戳（毫秒）
     * 记录开始计时的时间点，用于计算是否超时
     */
    long startTimeLimit = 0 ;

    /**
     * 私有构造方法
     * 实现单例模式，防止外部直接创建实例
     */
    private TimeLimit(){}

    /**
     * 单例实例
     * 全局唯一的TimeLimit实例
     */
    private static TimeLimit instant ;

    /**
     * 获取单例实例
     *
     * 使用懒加载方式创建单例实例，确保线程安全。
     *
     * @return TimeLimit的单例实例
     */
    public static TimeLimit getInstant(){
        if(instant == null){
            instant = new TimeLimit() ;
        }
        return  instant ;
    }

    /**
     * 超时回调接口引用
     * 保存当前设置的超时回调，用于超时时通知调用者
     */
    OnTimeOutCallBack onTimeOutCallBack ;

    /**
     * 超时回调接口
     *
     * 定义超时事件的回调方法，当检测到超时时会调用此接口。
     * 实现此接口的类可以在超时发生时执行相应的处理逻辑。
     */
    public interface OnTimeOutCallBack{
        /**
         * 超时回调方法
         *
         * 当超时事件发生时，此方法会被调用。
         * 实现类应在此方法中处理超时后的逻辑，如：
         * - 停止当前操作
         * - 显示超时提示
         * - 重试操作
         * - 清理资源等
         */
        void timeOut();
    }

    /**
     * 超时消息标识
     * Handler消息的what值，用于标识超时消息
     */
    private int TIME_OUT = 0 ;
    /**
     * 消息处理器
     *
     * 用于处理超时消息的Handler，运行在主线程中。
     * 当超时时间到达时，会接收到TIME_OUT消息并触发回调。
     */
    Handler handler = new Handler(){
        /**
         * 处理超时消息
         *
         * 当接收到超时消息时，检查并调用超时回调接口。
         *
         * @param msg 接收到的消息对象
         */
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            // 检查是否为超时消息
            if(msg.what == TIME_OUT){
                // 如果设置了超时回调，则调用超时处理方法
                if(onTimeOutCallBack != null){
                    onTimeOutCallBack.timeOut();
                }
            }
        }
    };

    /**
     * 开始超时计时
     *
     * 启动超时检测机制，设置超时回调并开始计时。
     * 超时时间固定为2000毫秒（2秒）。
     *
     * @param callBack 超时回调接口，当超时发生时会调用此回调
     */
    public void startTimeLimit(OnTimeOutCallBack callBack){
        // 保存超时回调接口
        onTimeOutCallBack= callBack;

        // 记录开始计时的时间戳
        startTimeLimit = System.currentTimeMillis() ;

        // 发送延时消息，2000毫秒后触发超时
        handler.sendEmptyMessageDelayed(TIME_OUT , 2000);
    }

    /**
     * 停止超时计时
     *
     * 取消之前设置的超时检测，移除待处理的超时消息。
     * 调用此方法后，即使时间到达也不会触发超时回调。
     */
    public void stopTimeLimit(){
        // 移除Handler中的超时消息，防止超时回调被触发
        handler.removeMessages(TIME_OUT);
    }

    /**
     * 同步检查是否已超时
     *
     * 通过比较当前时间与开始时间来判断是否已经超时。
     * 超时阈值为2000毫秒（2秒）。
     *
     * @return true表示已超时，false表示未超时
     */
    public boolean isTimeOut() {
        // 获取当前时间戳
        long currentTime = System.currentTimeMillis();

        // 计算时间差并与超时阈值比较
        if (Math.abs(currentTime - startTimeLimit) > 2000) {
            // 超过2秒，认为已超时
            return true;
        }

        // 未超时
        return false;
    }



}
