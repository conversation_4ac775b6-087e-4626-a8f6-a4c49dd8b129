package com.bes.business.Observable;


/**
 * 事件ID枚举类
 *
 * 功能描述：
 * - 定义应用程序中所有事件的唯一标识符
 * - 用于观察者模式中的事件类型识别
 * - 统一管理消息推送和事件处理的ID
 *
 * 设计目的：
 * - 避免使用魔法数字，提高代码可读性
 * - 集中管理所有事件类型，便于维护
 * - 为观察者模式提供类型安全的事件标识
 *
 * 事件分类：
 * - BLE相关事件：蓝牙低功耗设备的连接和数据传输
 * - BT相关事件：经典蓝牙设备的连接和数据传输
 * - 命令操作事件：设备控制和状态查询
 * - 数据更新事件：实时数据接收和处理
 *
 * 使用场景：
 * - PropertyObservable事件注册和分发
 * - Event接口实现中的事件类型判断
 * - 业务逻辑中的状态变化通知
 *
 * <AUTHOR> 消息推送，事件id，统一写在这
 * @version 1.0
 */
public enum EventID {

	// ==================== BLE（蓝牙低功耗）相关事件 ====================

	/**
	 * BLE重连操作事件
	 * 适用场景：中途断开连接后的重连，或已连接过设备的应用启动时重连
	 * 触发时机：应用检测到之前连接的设备可用时
	 */
	BLE_RECONNECT ,

	/**
	 * BLE新设备连接事件
	 * 适用场景：首次连接新设备，与重连操作在后端接口上基本相同，但在业务逻辑上有区别
	 * 触发时机：用户选择新的BLE设备进行连接时
	 */
	BLE_NEW_DEVICE_CONNECT ,

	/**
	 * BLE设备切换事件
	 * 适用场景：进入蓝牙搜索页面前调用，用于清空蓝牙地址缓存及断开当前设备连接
	 * 注意事项：断开连接回调处理时可能存在误判断，需要特别处理
	 */
	BLE_CHANGE_DEVICE ,

	/**
	 * BLE数据发送命令事件
	 * 数据格式：携带的数据为字节数组
	 * 用途：向BLE设备发送控制命令或数据
	 */
	BLE_SEND_CMD ,

	/**
	 * BLE连接成功事件
	 * 触发时机：BLE设备连接建立成功后
	 * 后续操作：通常会进行服务发现和特征值订阅
	 */
	BLE_CONNECTED ,

	/**
	 * BLE连接断开事件
	 * 触发时机：BLE设备连接断开时（主动或被动断开）
	 * 处理逻辑：清理连接资源，更新UI状态
	 */
	BLE_DISCONNED ,

	/**
	 * BLE连接异常事件
	 * 触发时机：BLE连接过程中发生错误时
	 * 错误类型：连接超时、设备不可用、权限不足等
	 */
	BLE_CONNECT_ERROR ,

    // ==================== 经典蓝牙（Classic Bluetooth）相关事件 ====================

	/**
	 * 经典蓝牙重连操作事件
	 * 适用场景：中途断开连接后的重连，或已连接过设备的应用启动时重连
	 * 与BLE_RECONNECT功能相似，但针对经典蓝牙设备
	 */
	BT_RECONNECT ,

	/**
	 * 经典蓝牙新设备连接事件
	 * 适用场景：首次连接新的经典蓝牙设备
	 * 与重连操作在后端接口上基本相同，但在业务逻辑上有区别
	 */
	BT_NEW_DEVICE_CONNECT ,

	/**
	 * 经典蓝牙设备切换事件
	 * 适用场景：进入蓝牙搜索页面前调用，清空蓝牙地址缓存及断开设备连接
	 * 注意事项：断开连接回调处理时可能存在误判断
	 */
	BT_CHANGE_DEVICE ,

	/**
	 * 经典蓝牙数据发送命令事件
	 * 数据格式：携带的数据为字节数组
	 * 传输协议：通常使用SPP（串口协议）进行数据传输
	 */
	BT_SEND_CMD ,

	/**
	 * 经典蓝牙连接成功事件
	 * 触发时机：经典蓝牙设备连接建立成功后
	 * 连接类型：通常是SPP连接，用于数据传输
	 */
	BT_CONNECTED ,

	/**
	 * 经典蓝牙连接断开事件
	 * 触发时机：经典蓝牙设备连接断开时
	 * 处理逻辑：清理SPP连接资源，更新连接状态
	 */
	BT_DISCONNED ,

	/**
	 * 经典蓝牙连接异常事件
	 * 触发时机：经典蓝牙连接过程中发生错误时
	 * 常见错误：配对失败、连接超时、设备不支持SPP等
	 */
	BT_CONNECT_ERROR ,
    // ==================== 蓝牙控制命令事件 ====================

    /**
     * SPP蓝牙断开连接命令事件
     * 用途：主动断开SPP蓝牙连接
     * 触发时机：用户点击断开按钮或应用需要切换设备时
     */
    CMD_BT_SPP_DISCONNECT,

    // ==================== 手机端设备控制指令事件 ====================

    /**
     * 电池状态显示命令事件
     * 功能：查询和显示设备电池电量状态
     * 数据内容：电池电量百分比、充电状态等
     */
    CMD_OP_BATTERY_STATUS_DISPLAY ,

    /**
     * 音效处理命令事件
     * 功能：控制设备的音效处理功能
     * 应用场景：开启/关闭音效增强、调整音效参数
     */
    CMD_OP_MERIDIAN_EFFECT,

    /**
     * 均衡器选择命令事件
     * 功能：选择和配置音频均衡器设置
     * 选项：不同的音效模式（如摇滚、古典、流行等）
     */
	CMD_OP_EQ_SELECT,

    /**
     * 音量增加控制命令事件
     * 功能：增加设备音量
     * 操作：向设备发送音量增加指令
     */
    CMD_OP_VOLUME_CONTROL_PLUS,

    /**
     * 音量减少控制命令事件
     * 功能：减少设备音量
     * 操作：向设备发送音量减少指令
     */
	CMD_OP_VOLUME_CONTROL_DEC,

    // ==================== 界面数据处理事件 ====================

    /**
     * 数据发送更新事件
     * 用途：界面显示发送数据的状态更新
     * 触发时机：向设备发送数据时
     */
    UPDATA_SEND_DATA,

    /**
     * 数据接收更新事件
     * 用途：界面显示接收数据的状态更新
     * 触发时机：从设备接收到数据时
     */
    UPDATA_RECE_DATA,

    // ==================== RSSI和信号质量相关事件 ====================

	/**
	 * RSSI读取命令事件
	 * 功能：开始读取设备的信号强度指示器
	 * 用途：监控蓝牙连接质量和信号强度
	 */
	CMD_OP_RSSI_READ,

	/**
	 * RSSI停止命令事件
	 * 功能：停止读取RSSI信号强度
	 * 触发时机：不再需要监控信号质量时
	 */
	CMD_OP_RSSI_STOP,

	/**
	 * 保活开始命令事件
	 * 功能：开始发送保活信号，维持连接状态
	 * 用途：防止长时间无数据传输导致的连接断开
	 */
	CMD_OP_KEEP_ALIVE_START,

	/**
	 * 保活停止命令事件
	 * 功能：停止发送保活信号
	 * 触发时机：准备断开连接或进入低功耗模式时
	 */
	CMD_OP_KEEP_ALIVE_STOP,

	/**
	 * RSSI信息接收更新事件（左声道）
	 * 数据内容：左声道的信号强度和质量信息
	 * 用途：实时显示左声道的连接质量
	 */
	UPDATA_RECE_RSSI_INFO,

	/**
	 * RSSI信息接收更新事件（右声道）
	 * 数据内容：右声道的信号强度和质量信息
	 * 用途：实时显示右声道的连接质量
	 */
	UPDATA_RECE_RSSI_INFO_R,

	/**
	 * RSSI信息接收更新事件（全部数据）
	 * 数据内容：包含左右声道的完整信号信息
	 * 用途：综合显示整体连接质量状态
	 */
	UPDATA_RECE_RSSI_INFO_ALL,

	/**
	 * 延迟信息接收更新事件
	 * 数据内容：音频传输延迟时间（微秒）
	 * 用途：监控和显示音频传输的实时延迟
	 */
	UPDATA_RECE_DELAY_INFO,

	/**
	 * 带宽更新事件
	 * 数据内容：当前传输带宽信息
	 * 用途：显示数据传输速率和带宽利用率
	 */
	UPDATA_BAND_WIDTN,

    // ==================== 数据接收控制事件 ====================

	/**
	 * 开始接收数据事件
	 * 触发时机：设备开始发送音频或其他数据时
	 * 用途：通知界面开始数据接收状态显示
	 */
	RECEIVE_START,

	/**
	 * 停止接收数据事件
	 * 触发时机：设备停止发送数据或连接断开时
	 * 用途：通知界面停止数据接收状态显示
	 */
	RECEIVE_STOP






}
