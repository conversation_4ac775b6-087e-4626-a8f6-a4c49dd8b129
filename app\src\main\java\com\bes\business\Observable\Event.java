package com.bes.business.Observable;

import java.util.Collection;

/**
 * 事件监听接口
 *
 * 功能描述：
 * - 定义观察者模式中观察者的行为规范
 * - 提供统一的事件处理接口
 * - 支持多种数据类型的事件传递
 *
 * 设计模式：
 * - 观察者模式中的Observer接口
 * - 定义了观察者接收通知时的标准行为
 * - 支持泛型，提供类型安全的数据传递
 *
 * 使用场景：
 * - Activity或Fragment实现此接口来接收业务事件
 * - 业务组件间的解耦通信
 * - UI更新的统一事件分发机制
 *
 * 实现要求：
 * - 实现类需要处理updateView方法中的各种事件类型
 * - 建议在主线程中处理UI相关的事件
 * - 需要根据eventId判断具体的事件类型并做相应处理
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface Event {
	/**
	 * 更新视图方法
	 * 当观察的事件发生时，此方法会被调用来通知观察者
	 *
	 * @param eventId 事件ID，标识具体发生的事件类型，用于判断需要更新哪个UI组件
	 * @param baseInfo 基础信息对象，包含事件相关的基础数据
	 * @param list 信息列表集合，当事件涉及多个数据对象时使用
	 * @param obj 可变参数对象数组，用于传递其他类型的事件数据，提供灵活的数据传递方式
	 */
	void updateView(EventID eventId, BaseInfo baseInfo, Collection<? extends BaseInfo> list, Object... obj);
}
