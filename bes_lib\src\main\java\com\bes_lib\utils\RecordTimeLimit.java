package com.bes_lib.utils;

/**
 * 按钮操作时间间隔限制工具类
 *
 * 该类用于防止用户频繁点击按钮或执行某些操作，通过时间间隔控制来避免：
 * 1. 重复提交数据
 * 2. 频繁的网络请求
 * 3. 快速连续的蓝牙操作
 * 4. 其他需要时间间隔控制的场景
 *
 * 工作原理：
 * - 记录上次操作的时间戳
 * - 检查当前操作与上次操作的时间间隔
 * - 如果间隔小于设定阈值，则认为操作过于频繁
 *
 * 注意：如果需要更严格的收发控制，建议使用超时机制
 *
 * Created by alloxuweibin on 2018/3/1.
 */
public class RecordTimeLimit {

    /**
     * 日志标签，用于调试输出
     */
    String TAG = "RecordTimeLimit";

    /**
     * 上次操作的时间戳（毫秒）
     * 用于计算时间间隔
     */
    long lastTime = 0 ;

    /**
     * 最短时间间隔常量（毫秒）
     * 设置为1500毫秒，即1.5秒的最小间隔
     */
    final long SHORT_TIME = 1500;

    /**
     * 重置时间记录
     *
     * 将当前时间设置为上次操作时间，通常在执行了有效操作后调用。
     * 这样可以重新开始计算下次操作的时间间隔。
     */
    public void resetTime(){
        // 记录当前时间戳作为新的基准时间
        lastTime = System.currentTimeMillis() ;
    }

    /**
     * 检查当前操作是否过于频繁
     *
     * 计算当前时间与上次操作时间的间隔，如果小于设定的最短间隔，
     * 则认为操作过于频繁，应该被阻止。
     *
     * @return true表示操作过于频繁，应该被阻止；false表示可以执行操作
     */
    public boolean isTooShortTime(){
        // 获取当前时间戳
        long currentTime = System.currentTimeMillis() ;

        // 计算时间间隔并与最短间隔比较
        if(Math.abs(currentTime - lastTime) <= SHORT_TIME){
            // 间隔太短，记录日志并返回true
            LogUtils.e(TAG ,"less than 1500ms isTooShortTime");
            return true ;
        }

        // 间隔足够长，可以执行操作
        LogUtils.e(TAG ,"> 1500ms you can do that");
        return  false ;
    }

}
