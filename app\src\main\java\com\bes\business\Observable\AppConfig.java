package com.bes.business.Observable;

import android.os.Handler;

/**
 * 应用程序配置类
 *
 * 功能描述：
 * - 提供全局的Handler对象，用于线程间通信
 * - 作为应用程序级别的配置中心
 * - 支持观察者模式中的消息分发机制
 *
 * 设计目的：
 * - 统一管理应用程序的全局配置
 * - 提供主线程Handler，确保UI更新在主线程执行
 * - 简化线程间的消息传递机制
 *
 * 使用场景：
 * - PropertyObservable中用于将事件回调切换到主线程
 * - 需要在子线程中更新UI时的线程切换
 * - 全局消息分发和处理
 *
 * <AUTHOR> on 17/4/18.
 * @version 1.0
 */
public class AppConfig {
    /**
     * 全局主线程Handler对象
     * 用于将子线程的操作切换到主线程执行，特别是UI更新操作
     * 在BesApplication中初始化，确保与主线程Looper绑定
     */
    public static Handler mHandler ;
}
