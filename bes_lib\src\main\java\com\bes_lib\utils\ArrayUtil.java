package com.bes_lib.utils;

import java.nio.ByteOrder;
import java.util.zip.CRC32;

/**
 * 数组操作工具类
 *
 * 该工具类提供了丰富的字节数组和数据类型转换操作，主要功能包括：
 * 1. 字节数组的基础操作（提取、比较、包含检查等）
 * 2. 数据完整性校验（CRC32、异或校验等）
 * 3. 数据格式转换（字节转十六进制、ASCII转换等）
 * 4. 数据类型转换（short/int与字节数组互转）
 * 5. 字节序处理（大端序/小端序转换）
 * 6. 字符串与字节数组的相互转换
 *
 * 设计特点：
 * - 静态方法设计，方便全局调用
 * - 完整的空值检查和边界处理
 * - 支持多种数据格式和编码方式
 * - 兼容不同CPU架构的字节序
 *
 * 使用场景：
 * - 蓝牙通信数据的解析和封装
 * - 网络协议数据的处理
 * - 文件数据的读写和转换
 * - 数据完整性验证
 * - 调试信息的格式化输出
 *
 * Created by zhaowanxing on 2017/4/23.
 */
public class ArrayUtil {

    /**
     * 从字节数组中提取指定范围的子数组
     *
     * 从源数组的指定位置开始，提取指定长度的字节数据。
     * 此方法使用System.arraycopy确保高效的数组复制。
     *
     * @param data 源字节数组
     * @param start 开始位置（从0开始）
     * @param length 要提取的字节长度
     * @return 提取的字节子数组
     */
    public static byte[] extractBytes(byte[] data, int start, int length) {
        // 创建目标数组
        byte[] bytes = new byte[length];

        // 使用系统方法高效复制数组
        System.arraycopy(data, start, bytes, 0, length);
        return bytes;
    }

    /**
     * 比较两个字节数组是否完全相等
     *
     * 逐字节比较两个数组的内容，包括完整的空值处理。
     * 此方法比Arrays.equals()提供了更详细的比较逻辑。
     *
     * @param array_1 第一个字节数组
     * @param array_2 第二个字节数组
     * @return true表示两个数组完全相等，false表示不相等
     */
    public static boolean isEqual(byte[] array_1, byte[] array_2) {
        // 处理第一个数组为null的情况
        if (array_1 == null) {
            return array_2 == null;
        }

        // 处理第二个数组为null的情况
        if (array_2 == null) {
            return false;
        }

        // 检查是否为同一个对象引用
        if (array_1 == array_2) {
            return true;
        }

        // 检查长度是否相等
        if (array_1.length != array_2.length) {
            return false;
        }

        // 逐字节比较内容
        for (int i = 0; i < array_1.length; i++) {
            if (array_1[i] != array_2[i]) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查父数组是否包含子数组
     *
     * 判断父字节数组中是否包含指定的子字节数组。
     * 此方法通过字符串转换来实现包含检查。
     *
     * @param parent 父字节数组
     * @param child 要查找的子字节数组
     * @return true表示包含，false表示不包含
     */
    public static boolean contains(byte[] parent, byte[] child) {
        // 处理父数组为null的情况
        if (parent == null) {
            return child == null;
        }

        // 处理子数组为null或空的情况
        if (child == null || child.length == 0) {
            return true;
        }

        // 检查是否为同一个对象引用
        if (parent == child) {
            return true;
        }

        // 转换为字符串进行包含检查
        return new String(parent).contains(new String(child));
    }

    /**
     * 计算字节数组的CRC32校验值
     *
     * 使用CRC32算法计算指定范围字节数据的校验值。
     * CRC32是一种常用的循环冗余校验算法，用于数据完整性验证。
     *
     * @param data 要计算校验值的字节数组
     * @param offset 开始计算的偏移位置
     * @param length 要计算的字节长度
     * @return CRC32校验值（32位无符号整数）
     */
    public static long crc32(byte[] data, int offset, int length) {
        // 创建CRC32校验器
        CRC32 crc32 = new CRC32();

        // 更新指定范围的数据
        crc32.update(data, offset, length);

        // 返回校验值
        return crc32.getValue();
    }

    /**
     * 计算字节数组的异或校验和
     *
     * 对指定长度的字节数据进行异或运算，得到校验和。
     * 异或校验是一种简单快速的数据完整性检查方法。
     *
     * @param data 要计算校验和的字节数组
     * @param len 要计算的字节长度
     * @return 异或校验和结果
     */
    public static byte checkSum(byte[] data, int len) {
        byte sum = (byte) 0;

        // 对指定长度的数据进行异或运算
        for (int i = 0; i < len; i++) {
            sum ^= data[i];
        }
        return sum;
    }

    /**
     * 将字节数组转换为十六进制字符串（带逗号分隔）
     *
     * 将每个字节转换为两位十六进制表示，并用逗号分隔。
     * 适用于调试输出和数据可视化。
     *
     * @param data 要转换的字节数组
     * @return 十六进制字符串，格式如"01,2a,ff,"
     */
    public static String toHex(byte[] data) {
        StringBuffer buffer = new StringBuffer();

        // 遍历每个字节，转换为十六进制
        for (int i = 0; i < data.length; i++) {
            buffer.append(String.format("%02x", data[i])).append(",");
        }
        return buffer.toString();
    }

    /**
     * 将short数组转换为十六进制字符串（带逗号分隔）
     *
     * 将每个short值按字节拆分，转换为十六进制表示。
     * 每个short值产生两个字节的十六进制输出。
     *
     * @param data 要转换的short数组
     * @return 十六进制字符串，每个short产生4个十六进制字符
     */
    public static String toHex(short[] data) {
        StringBuffer buffer = new StringBuffer();

        // 遍历每个short值
        for (int i = 0; i < data.length; i++) {
            // 输出低字节
            buffer.append(String.format("%02x", (data[i]&0xff))).append(",");
            // 输出高字节
            buffer.append(String.format("%02x", ((data[i] >> 8)&0xff))).append(",");
        }
        return buffer.toString();
    }

    /**
     * 将字节数组转换为ASCII字符串
     *
     * 将每个字节直接转换为对应的ASCII字符。
     * 适用于处理文本数据或可打印字符。
     *
     * @param data 要转换的字节数组
     * @return ASCII字符串
     */
    public static String toASCII(byte[] data) {
        StringBuffer buffer = new StringBuffer();

        // 将每个字节转换为字符
        for (int i = 0; i < data.length; i++) {
            buffer.append((char)data[i]);
        }
        return buffer.toString();
    }

    public static byte[] Shorts2Bytes(short[] s) {
        byte bLength = 2;
        byte[] buf = new byte[s.length * bLength];
        for (int iLoop = 0; iLoop < s.length; iLoop++) {
            byte[] temp = getBytes(s[iLoop]);
            for (int jLoop = 0; jLoop < bLength; jLoop++) {
                buf[iLoop * bLength + jLoop] = temp[jLoop];
            }
        }
        return buf;
    }

    public static boolean testCPU() {
        if (ByteOrder.nativeOrder() == ByteOrder.BIG_ENDIAN) {
            // System.out.println("is big ending");
            return true;
        } else {
            // System.out.println("is little ending");
            return false;
        }
    }

    public static byte[] getBytes(short s, boolean bBigEnding) {
        byte[] buf = new byte[2];
        if (bBigEnding)
            for (int i = buf.length - 1; i >= 0; i--) {
                buf[i] = (byte) (s & 0x00ff);
                s >>= 8;
            }
        else
            for (int i = 0; i < buf.length; i++) {
                buf[i] = (byte) (s & 0x00ff);
                s >>= 8;
            }
        return buf;
    }

    public static byte[] getBytes(short s) {
        return getBytes(s, testCPU());
    }

    public static boolean startsWith(byte[] data, byte[] param) {
        if (data == null) {
            return param == null;
        }
        if (param == null) {
            return true;
        }
        if (data.length < param.length) {
            return false;
        }
        for (int i = 0; i < param.length; i++) {
            if (data[i] != param[i]) {
                return false;
            }
        }
        return true;
    }

    public static byte[] toBytes(String str) {
        if(str == null || str.trim().equals("")) {
            return new byte[0];
        }
        str = str.replace(",","");//与toHex函数对应
        if(str == null || str.trim().equals("")) {
            return new byte[0];
        }
        byte[] bytes = new byte[str.length() / 2];
        for(int i = 0; i < str.length() / 2; i++) {
            String subStr = str.substring(i * 2, i * 2 + 2);
            bytes[i] = (byte) Integer.parseInt(subStr, 16);
        }
        return bytes;
    }
    public static int  getRssiValue(byte data) {
       return  Integer.valueOf(data);
    }

    //+++/将byte转换成16进制字符串
    public static String bytesToHexString(byte[] src){
        StringBuilder stringBuilder = new StringBuilder("");
        if (src == null || src.length <= 0) {
            return null;
        }
        for (int i = 0; i < src.length; i++) {
            int v = src[i] & 0xFF;
            String hv = Integer.toHexString(v);
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
        }
        return stringBuilder.toString();
    }

    public static byte[] intToBytesLittle(int value) {
        byte[] src = new byte[4];
        src[3] = (byte) ((value >> 24) & 0xFF);
        src[2] = (byte) ((value >> 16) & 0xFF);
        src[1] = (byte) ((value >> 8) & 0xFF);
        src[0] = (byte) (value & 0xFF);
        return src;
    }

    public static int bytesToIntLittle(byte[] src) {
        int value = 0;
        value = (int) ((src[0] & 0xFF)
                | ((src[1] << 8) & 0xFF00)
                | ((src[2] << 16) & 0xFF0000)
                | ((src[3] << 24) & 0xFF000000));
        return value;


    }

}
