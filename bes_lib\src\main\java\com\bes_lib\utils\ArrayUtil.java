package com.bes_lib.utils;

import java.nio.ByteOrder;
import java.util.zip.CRC32;

/**
 * 数组操作工具类
 *
 * 该工具类提供了丰富的字节数组和数据类型转换操作，主要功能包括：
 * 1. 字节数组的基础操作（提取、比较、包含检查等）
 * 2. 数据完整性校验（CRC32、异或校验等）
 * 3. 数据格式转换（字节转十六进制、ASCII转换等）
 * 4. 数据类型转换（short/int与字节数组互转）
 * 5. 字节序处理（大端序/小端序转换）
 * 6. 字符串与字节数组的相互转换
 *
 * 设计特点：
 * - 静态方法设计，方便全局调用
 * - 完整的空值检查和边界处理
 * - 支持多种数据格式和编码方式
 * - 兼容不同CPU架构的字节序
 *
 * 使用场景：
 * - 蓝牙通信数据的解析和封装
 * - 网络协议数据的处理
 * - 文件数据的读写和转换
 * - 数据完整性验证
 * - 调试信息的格式化输出
 *
 * Created by zhaowanxing on 2017/4/23.
 */
public class ArrayUtil {

    /**
     * 从字节数组中提取指定范围的子数组
     *
     * 从源数组的指定位置开始，提取指定长度的字节数据。
     * 此方法使用System.arraycopy确保高效的数组复制。
     *
     * @param data 源字节数组
     * @param start 开始位置（从0开始）
     * @param length 要提取的字节长度
     * @return 提取的字节子数组
     */
    public static byte[] extractBytes(byte[] data, int start, int length) {
        // 创建目标数组
        byte[] bytes = new byte[length];

        // 使用系统方法高效复制数组
        System.arraycopy(data, start, bytes, 0, length);
        return bytes;
    }

    /**
     * 比较两个字节数组是否完全相等
     *
     * 逐字节比较两个数组的内容，包括完整的空值处理。
     * 此方法比Arrays.equals()提供了更详细的比较逻辑。
     *
     * @param array_1 第一个字节数组
     * @param array_2 第二个字节数组
     * @return true表示两个数组完全相等，false表示不相等
     */
    public static boolean isEqual(byte[] array_1, byte[] array_2) {
        // 处理第一个数组为null的情况
        if (array_1 == null) {
            return array_2 == null;
        }

        // 处理第二个数组为null的情况
        if (array_2 == null) {
            return false;
        }

        // 检查是否为同一个对象引用
        if (array_1 == array_2) {
            return true;
        }

        // 检查长度是否相等
        if (array_1.length != array_2.length) {
            return false;
        }

        // 逐字节比较内容
        for (int i = 0; i < array_1.length; i++) {
            if (array_1[i] != array_2[i]) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查父数组是否包含子数组
     *
     * 判断父字节数组中是否包含指定的子字节数组。
     * 此方法通过字符串转换来实现包含检查。
     *
     * @param parent 父字节数组
     * @param child 要查找的子字节数组
     * @return true表示包含，false表示不包含
     */
    public static boolean contains(byte[] parent, byte[] child) {
        // 处理父数组为null的情况
        if (parent == null) {
            return child == null;
        }

        // 处理子数组为null或空的情况
        if (child == null || child.length == 0) {
            return true;
        }

        // 检查是否为同一个对象引用
        if (parent == child) {
            return true;
        }

        // 转换为字符串进行包含检查
        return new String(parent).contains(new String(child));
    }

    /**
     * 计算字节数组的CRC32校验值
     *
     * 使用CRC32算法计算指定范围字节数据的校验值。
     * CRC32是一种常用的循环冗余校验算法，用于数据完整性验证。
     *
     * @param data 要计算校验值的字节数组
     * @param offset 开始计算的偏移位置
     * @param length 要计算的字节长度
     * @return CRC32校验值（32位无符号整数）
     */
    public static long crc32(byte[] data, int offset, int length) {
        // 创建CRC32校验器
        CRC32 crc32 = new CRC32();

        // 更新指定范围的数据
        crc32.update(data, offset, length);

        // 返回校验值
        return crc32.getValue();
    }

    /**
     * 计算字节数组的异或校验和
     *
     * 对指定长度的字节数据进行异或运算，得到校验和。
     * 异或校验是一种简单快速的数据完整性检查方法。
     *
     * @param data 要计算校验和的字节数组
     * @param len 要计算的字节长度
     * @return 异或校验和结果
     */
    public static byte checkSum(byte[] data, int len) {
        byte sum = (byte) 0;

        // 对指定长度的数据进行异或运算
        for (int i = 0; i < len; i++) {
            sum ^= data[i];
        }
        return sum;
    }

    /**
     * 将字节数组转换为十六进制字符串（带逗号分隔）
     *
     * 将每个字节转换为两位十六进制表示，并用逗号分隔。
     * 适用于调试输出和数据可视化。
     *
     * @param data 要转换的字节数组
     * @return 十六进制字符串，格式如"01,2a,ff,"
     */
    public static String toHex(byte[] data) {
        StringBuffer buffer = new StringBuffer();

        // 遍历每个字节，转换为十六进制
        for (int i = 0; i < data.length; i++) {
            buffer.append(String.format("%02x", data[i])).append(",");
        }
        return buffer.toString();
    }

    /**
     * 将short数组转换为十六进制字符串（带逗号分隔）
     *
     * 将每个short值按字节拆分，转换为十六进制表示。
     * 每个short值产生两个字节的十六进制输出。
     *
     * @param data 要转换的short数组
     * @return 十六进制字符串，每个short产生4个十六进制字符
     */
    public static String toHex(short[] data) {
        StringBuffer buffer = new StringBuffer();

        // 遍历每个short值
        for (int i = 0; i < data.length; i++) {
            // 输出低字节
            buffer.append(String.format("%02x", (data[i]&0xff))).append(",");
            // 输出高字节
            buffer.append(String.format("%02x", ((data[i] >> 8)&0xff))).append(",");
        }
        return buffer.toString();
    }

    /**
     * 将字节数组转换为ASCII字符串
     *
     * 将每个字节直接转换为对应的ASCII字符。
     * 适用于处理文本数据或可打印字符。
     *
     * @param data 要转换的字节数组
     * @return ASCII字符串
     */
    public static String toASCII(byte[] data) {
        StringBuffer buffer = new StringBuffer();

        // 将每个字节转换为字符
        for (int i = 0; i < data.length; i++) {
            buffer.append((char)data[i]);
        }
        return buffer.toString();
    }

    /**
     * 将short数组转换为字节数组
     *
     * 将short数组中的每个元素转换为对应的字节表示。
     * 每个short值占用2个字节，按照系统字节序进行转换。
     *
     * @param s 要转换的short数组
     * @return 转换后的字节数组，长度为原数组长度的2倍
     */
    public static byte[] Shorts2Bytes(short[] s) {
        byte bLength = 2; // 每个short占用2个字节
        byte[] buf = new byte[s.length * bLength];

        // 遍历每个short值
        for (int iLoop = 0; iLoop < s.length; iLoop++) {
            // 将short转换为字节数组
            byte[] temp = getBytes(s[iLoop]);

            // 将字节复制到目标数组
            for (int jLoop = 0; jLoop < bLength; jLoop++) {
                buf[iLoop * bLength + jLoop] = temp[jLoop];
            }
        }
        return buf;
    }

    /**
     * 检测CPU字节序类型
     *
     * 检查当前系统的CPU是大端序还是小端序。
     * 字节序影响多字节数据在内存中的存储顺序。
     *
     * @return true表示大端序（Big Endian），false表示小端序（Little Endian）
     */
    public static boolean testCPU() {
        if (ByteOrder.nativeOrder() == ByteOrder.BIG_ENDIAN) {
            // 大端序：高位字节存储在低地址
            return true;
        } else {
            // 小端序：低位字节存储在低地址
            return false;
        }
    }

    /**
     * 将short值转换为字节数组（指定字节序）
     *
     * 根据指定的字节序将short值转换为2字节的字节数组。
     * 支持大端序和小端序两种转换方式。
     *
     * @param s 要转换的short值
     * @param bBigEnding 字节序标志，true为大端序，false为小端序
     * @return 包含2个字节的数组
     */
    public static byte[] getBytes(short s, boolean bBigEnding) {
        byte[] buf = new byte[2];

        if (bBigEnding) {
            // 大端序：高位字节在前
            for (int i = buf.length - 1; i >= 0; i--) {
                buf[i] = (byte) (s & 0x00ff);
                s >>= 8; // 右移8位，处理下一个字节
            }
        } else {
            // 小端序：低位字节在前
            for (int i = 0; i < buf.length; i++) {
                buf[i] = (byte) (s & 0x00ff);
                s >>= 8; // 右移8位，处理下一个字节
            }
        }
        return buf;
    }

    /**
     * 将short值转换为字节数组（使用系统字节序）
     *
     * 使用当前系统的字节序将short值转换为字节数组。
     * 这是getBytes(short, boolean)方法的便捷版本。
     *
     * @param s 要转换的short值
     * @return 按系统字节序转换的字节数组
     */
    public static byte[] getBytes(short s) {
        // 使用系统检测到的字节序进行转换
        return getBytes(s, testCPU());
    }

    /**
     * 检查字节数组是否以指定的字节序列开头
     *
     * 判断数据数组的开头是否与参数数组完全匹配。
     * 常用于协议头部验证和数据格式检查。
     *
     * @param data 要检查的数据数组
     * @param param 期望的开头字节序列
     * @return true表示以指定序列开头，false表示不匹配
     */
    public static boolean startsWith(byte[] data, byte[] param) {
        // 处理数据数组为null的情况
        if (data == null) {
            return param == null;
        }

        // 处理参数数组为null的情况
        if (param == null) {
            return true;
        }

        // 检查数据长度是否足够
        if (data.length < param.length) {
            return false;
        }

        // 逐字节比较开头部分
        for (int i = 0; i < param.length; i++) {
            if (data[i] != param[i]) {
                return false;
            }
        }
        return true;
    }

    /**
     * 将十六进制字符串转换为字节数组
     *
     * 将十六进制格式的字符串转换回字节数组。
     * 此方法与toHex()方法互为逆操作，支持带逗号分隔的格式。
     *
     * @param str 十六进制字符串，可包含逗号分隔符
     * @return 转换后的字节数组，如果输入无效则返回空数组
     */
    public static byte[] toBytes(String str) {
        // 检查输入字符串是否有效
        if(str == null || str.trim().equals("")) {
            return new byte[0];
        }

        // 移除逗号分隔符，与toHex函数对应
        str = str.replace(",","");
        if(str == null || str.trim().equals("")) {
            return new byte[0];
        }

        // 创建结果数组，每2个字符代表1个字节
        byte[] bytes = new byte[str.length() / 2];

        // 逐个解析十六进制字符对
        for(int i = 0; i < str.length() / 2; i++) {
            String subStr = str.substring(i * 2, i * 2 + 2);
            bytes[i] = (byte) Integer.parseInt(subStr, 16);
        }
        return bytes;
    }

    /**
     * 获取RSSI信号强度值
     *
     * 将字节类型的RSSI数据转换为整数值。
     * RSSI（Received Signal Strength Indicator）用于表示接收信号强度。
     *
     * @param data RSSI字节数据
     * @return RSSI整数值
     */
    public static int getRssiValue(byte data) {
       // 将byte转换为Integer对象再获取int值
       return Integer.valueOf(data);
    }

    /**
     * 将字节数组转换为十六进制字符串（无分隔符）
     *
     * 将字节数组转换为连续的十六进制字符串，不包含分隔符。
     * 每个字节转换为2位十六进制字符，适用于紧凑的数据表示。
     *
     * @param src 要转换的字节数组
     * @return 十六进制字符串，如果输入无效则返回null
     */
    public static String bytesToHexString(byte[] src){
        StringBuilder stringBuilder = new StringBuilder("");

        // 检查输入数组是否有效
        if (src == null || src.length <= 0) {
            return null;
        }

        // 遍历每个字节进行转换
        for (int i = 0; i < src.length; i++) {
            // 将字节转换为无符号整数
            int v = src[i] & 0xFF;

            // 转换为十六进制字符串
            String hv = Integer.toHexString(v);

            // 如果长度不足2位，前面补0
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
        }
        return stringBuilder.toString();
    }

    /**
     * 将整数转换为字节数组（小端序）
     *
     * 将32位整数按小端序（低位字节在前）转换为4字节数组。
     * 小端序是x86架构常用的字节序格式。
     *
     * @param value 要转换的整数值
     * @return 4字节的数组，按小端序排列
     */
    public static byte[] intToBytesLittle(int value) {
        byte[] src = new byte[4];

        // 小端序：低位字节在前
        src[3] = (byte) ((value >> 24) & 0xFF); // 最高字节
        src[2] = (byte) ((value >> 16) & 0xFF); // 次高字节
        src[1] = (byte) ((value >> 8) & 0xFF);  // 次低字节
        src[0] = (byte) (value & 0xFF);         // 最低字节
        return src;
    }

    /**
     * 将字节数组转换为整数（小端序）
     *
     * 将4字节的小端序数组转换回32位整数。
     * 此方法与intToBytesLittle()互为逆操作。
     *
     * @param src 4字节的小端序数组
     * @return 转换后的整数值
     */
    public static int bytesToIntLittle(byte[] src) {
        int value = 0;

        // 小端序解析：低位字节在前
        value = (int) ((src[0] & 0xFF)           // 最低字节
                | ((src[1] << 8) & 0xFF00)       // 次低字节左移8位
                | ((src[2] << 16) & 0xFF0000)    // 次高字节左移16位
                | ((src[3] << 24) & 0xFF000000)); // 最高字节左移24位
        return value;
    }

}
