package com.bes_lib.bluetooth.scanner;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;

import com.bes_lib.bluetooth.callback.ScanCallback;


/**
 * 经典蓝牙设备扫描器实现
 *
 * 该类实现了经典蓝牙(Classic Bluetooth)设备的扫描功能。
 * 经典蓝牙主要用于音频设备、输入设备等传统蓝牙应用场景。
 *
 * 主要特点：
 * 1. 使用BluetoothAdapter.startDiscovery()进行设备发现
 * 2. 通过BroadcastReceiver监听蓝牙扫描相关的系统广播
 * 3. 支持扫描开始、设备发现、扫描结束等事件处理
 * 4. 适用于所有支持蓝牙的Android设备
 *
 * 工作原理：
 * - 注册广播接收器监听蓝牙扫描事件
 * - 调用系统蓝牙适配器的发现方法
 * - 通过广播接收器接收扫描结果
 * - 将结果转发给上层应用
 *
 * 注意事项：
 * - 经典蓝牙扫描比BLE扫描更耗电
 * - 扫描过程中会影响蓝牙音频等其他蓝牙功能
 * - 需要BLUETOOTH和BLUETOOTH_ADMIN权限
 *
 * Created by alloxuweibin on 2017/12/11.
 */
public class ClassicScanner extends BaseScanner {

    /**
     * Android上下文对象
     * 用于注册和注销广播接收器，以及访问系统服务
     */
    private Context mContext;

    /**
     * 构造方法
     *
     * 创建经典蓝牙扫描器实例，初始化广播接收器。
     * 在创建时会自动注册蓝牙扫描相关的广播接收器，
     * 用于监听扫描状态变化和设备发现事件。
     *
     * @param context Android上下文对象，用于注册广播接收器和获取系统服务
     */
    public ClassicScanner(Context context) {
        // 调用父类构造方法，初始化基础扫描器功能
        super(context);

        // 保存上下文引用，用于广播接收器的注册和注销
        mContext = context;

        // 初始化并注册广播接收器，监听蓝牙扫描事件
        initReceiver();
    }

    /**
     * 开始经典蓝牙设备扫描
     *
     * 启动经典蓝牙设备发现过程。此方法会检查当前扫描状态，
     * 避免重复扫描，然后调用系统蓝牙适配器的startDiscovery()方法。
     * 扫描结果将通过广播接收器异步接收。
     *
     * @param callback 扫描结果回调接口
     */
    @Override
    public void startScan(ScanCallback callback) {
        // 调用父类方法保存回调接口
        super.startScan(callback);

        // 检查是否已在扫描中，避免重复启动
        if (isScanning())
            return;

        // 启动经典蓝牙设备发现过程
        // 扫描开始和结束事件将通过广播接收器通知
        getBluetoothAdapter().startDiscovery();
    }

    /**
     * 停止经典蓝牙设备扫描
     *
     * 取消正在进行的蓝牙设备发现过程。此方法会检查当前扫描状态，
     * 只有在扫描中时才执行停止操作，然后调用系统蓝牙适配器的
     * cancelDiscovery()方法。
     */
    @Override
    public void stopScan() {
        // 检查是否正在扫描，未扫描时直接返回
        if (!isScanning())
            return;

        // 取消经典蓝牙设备发现过程
        // 扫描结束事件将通过广播接收器通知
        getBluetoothAdapter().cancelDiscovery();
    }

    /**
     * 关闭扫描器并释放资源
     *
     * 注销广播接收器，释放系统资源。在不再需要使用扫描器时
     * 必须调用此方法，以避免内存泄漏和资源占用。
     */
    @Override
    public void close() {
        // 注销广播接收器，释放系统资源
        mContext.unregisterReceiver(mReceiver);
    }

    /**
     * 初始化并注册广播接收器
     *
     * 创建意图过滤器，添加蓝牙扫描相关的广播动作，
     * 然后注册广播接收器来监听这些事件。
     *
     * 监听的广播动作包括：
     * - ACTION_DISCOVERY_STARTED: 扫描开始
     * - ACTION_DISCOVERY_FINISHED: 扫描结束
     * - ACTION_FOUND: 发现设备
     */
    private void initReceiver() {
        // 创建意图过滤器，用于指定要监听的广播动作
        IntentFilter filter = new IntentFilter();

        // 添加扫描开始广播动作
        filter.addAction(BluetoothAdapter.ACTION_DISCOVERY_STARTED);

        // 添加扫描结束广播动作
        filter.addAction(BluetoothAdapter.ACTION_DISCOVERY_FINISHED);

        // 添加发现设备广播动作
        filter.addAction(BluetoothDevice.ACTION_FOUND);

        // 注册广播接收器，开始监听蓝牙扫描事件
        mContext.registerReceiver(mReceiver, filter);
    }

    /**
     * 蓝牙扫描事件广播接收器
     *
     * 该广播接收器负责监听系统发出的蓝牙扫描相关广播，
     * 包括扫描开始、扫描结束和发现设备等事件。
     * 接收到广播后，会调用相应的回调方法通知上层应用。
     */
    private BroadcastReceiver mReceiver = new BroadcastReceiver() {
        /**
         * 广播接收回调方法
         *
         * 当系统发出蓝牙扫描相关广播时，此方法会被调用。
         * 根据不同的广播动作，执行相应的处理逻辑。
         *
         * @param context 广播上下文
         * @param intent 包含广播信息的意图对象
         */
        @Override
        public void onReceive(Context context, Intent intent) {
            // 根据广播动作类型进行不同的处理
            switch (intent.getAction()) {
                case BluetoothAdapter.ACTION_DISCOVERY_STARTED:
                    // 收到扫描开始广播，通知上层应用扫描已开始
                    onScanStart();
                    break;

                case BluetoothAdapter.ACTION_DISCOVERY_FINISHED:
                    // 收到扫描结束广播，通知上层应用扫描已结束
                    onScanFinish();
                    break;

                case BluetoothDevice.ACTION_FOUND:
                    // 收到发现设备广播，提取设备信息并通知上层应用

                    // 从广播意图中提取发现的蓝牙设备对象
                    BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);

                    // 从广播意图中提取信号强度，如果没有则使用默认值-100dBm
                    int rssi = intent.getShortExtra(BluetoothDevice.EXTRA_RSSI, (short) -100);

                    // 调用设备发现回调，经典蓝牙没有扫描记录数据，传入null
                    onFound(device, rssi, null);
                    break;
            }
        }
    };
}
