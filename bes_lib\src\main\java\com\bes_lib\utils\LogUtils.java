package com.bes_lib.utils;

import android.text.format.DateFormat;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.io.Writer;

/**
 * 日志工具类
 *
 * 该工具类提供了完整的日志管理功能，主要特性包括：
 * 1. 多级别日志控制（VERBOSE、INFO、ERROR、WARNING、DEBUG）
 * 2. 日志输出规则的动态配置
 * 3. 日志文件的本地保存和管理
 * 4. 专用日志文件分类（OTA、BLE、经典蓝牙等）
 * 5. 异常信息的格式化输出
 * 6. 时间戳自动添加功能
 *
 * 设计特点：
 * - 静态方法设计，全局可用
 * - 支持控制台输出和文件保存双重模式
 * - 灵活的日志级别开关控制
 * - 自动时间戳和标签格式化
 * - 专门的蓝牙通信日志支持
 *
 * 使用场景：
 * - 应用运行时的调试信息记录
 * - 蓝牙通信过程的详细日志
 * - OTA升级过程的状态跟踪
 * - 异常和错误信息的持久化保存
 * - 性能分析和问题排查
 *
 * 文件分类：
 * - Log.txt: 通用日志文件
 * - ota.txt: OTA升级相关日志
 * - HwCloudLog.txt: 华为云服务日志
 * - ble/spp: 蓝牙通信专用日志
 */
public class LogUtils
{
	/**
	 * OTA升级日志文件名
	 * 用于记录OTA升级过程中的所有操作和状态信息
	 */
	static String PROJECT_OTA = "ota.txt" ;

	/**
	 * 通用日志文件名
	 * 用于记录应用的一般运行日志和调试信息
	 */
	static String PROJECT_LOG = "Log.txt" ;

	/**
	 * 华为云服务日志文件名
	 * 用于记录与华为云服务交互的相关日志
	 */
	static String PROJECT_HW_CLOUD_LOG = "HwCloudLog.txt" ;

	/**
	 * VERBOSE级别日志开关
	 * 控制详细信息日志的输出
	 */
	static boolean V_DEBUG = true;

	/**
	 * INFO级别日志开关
	 * 控制一般信息日志的输出
	 */
	static boolean I_DEBUG = true;

	/**
	 * ERROR级别日志开关
	 * 控制错误信息日志的输出
	 */
	static boolean E_DEBUG = true;

	/**
	 * WARNING级别日志开关
	 * 控制警告信息日志的输出
	 */
	static boolean W_DEBUG = true;

	/**
	 * 检查并设置日志输出规则
	 *
	 * 内部方法，用于在Beta版本或线上版本中设置日志输出规则。
	 * 当前实现为开发模式，所有级别的日志都启用。
	 * 在正式发布时可以根据需要调整各级别的开关状态。
	 */
	private static void checkBetaOrLine(){
		// 开发阶段启用所有级别的日志
		V_DEBUG = true;
		I_DEBUG = true;
		E_DEBUG = true;
		W_DEBUG = true;
	}

	/**
	 * 初始化日志工具的输出规则
	 *
	 * 设置各个日志级别的开关状态，允许应用在运行时动态控制
	 * 哪些级别的日志需要输出，哪些需要屏蔽。
	 *
	 * @param w_log WARNING级别日志开关，true启用，false禁用
	 * @param v_log VERBOSE级别日志开关，true启用，false禁用
	 * @param i_log INFO级别日志开关，true启用，false禁用
	 * @param e_log ERROR级别日志开关，true启用，false禁用
	 */
	public static void InitLogUtils(boolean w_log ,boolean v_log, boolean i_log, boolean e_log)
	{
		// 设置各级别日志的开关状态
		V_DEBUG = v_log ;
		I_DEBUG = i_log ;
		E_DEBUG = e_log ;
		W_DEBUG = w_log ;
	}

	public static void v(String tag, String msg)
	{
		checkBetaOrLine();
		if (V_DEBUG && msg != null)
		{
			android.util.Log.v(tag, msg);
		}

	}

	public static void v_write(String tag, String msg)
	{
		checkBetaOrLine();
		if (V_DEBUG && msg != null)
		{
			android.util.Log.v(tag, msg);
			FileUtils.writeTOfileAndActiveClear(PROJECT_LOG,
						DateFormat.format("yyyy-MM-dd HH:mm:ss", System.currentTimeMillis()) + "V<" + tag + ">---" + msg);
        }
	}

	public static void v(String tag, String msg, Throwable t)
	{
		checkBetaOrLine();
		if (V_DEBUG && msg != null)
		{
			android.util.Log.v(tag, msg, t);
		}

    }

	public static void v_write(String tag, String msg, Throwable t)
	{
		checkBetaOrLine();
		if (V_DEBUG && msg != null)
		{
			android.util.Log.v(tag, msg, t);
			FileUtils.writeTOfileAndActiveClear(PROJECT_LOG,
					DateFormat.format("yyyy-MM-dd HH:mm:ss", System.currentTimeMillis()) + "Vt<" + tag + ">---" + msg);
		}

	}
	public static void w(String tag, String msg)
	{
		checkBetaOrLine();
		if (V_DEBUG && msg != null)
		{
			android.util.Log.w(tag, msg);
		}

	}

	public static void w_write(String tag, String msg)
	{
		checkBetaOrLine();
		if (V_DEBUG && msg != null)
		{
			android.util.Log.w(tag, msg);
			FileUtils.writeTOfileAndActiveClear(PROJECT_LOG,
						DateFormat.format("yyyy-MM-dd HH:mm:ss", System.currentTimeMillis()) + "V<" + tag + ">---" + msg);
        }
	}

	public static void w(String tag, String msg, Throwable t)
	{
		checkBetaOrLine();
		if (V_DEBUG && msg != null)
		{
			android.util.Log.w(tag, msg, t);
		}

    }

	public static void w_write(String tag, String msg, Throwable t)
	{
		checkBetaOrLine();
		if (V_DEBUG && msg != null)
		{
			android.util.Log.w(tag, msg, t);
			FileUtils.writeTOfileAndActiveClear(PROJECT_LOG,
					DateFormat.format("yyyy-MM-dd HH:mm:ss", System.currentTimeMillis()) + "Vt<" + tag + ">---" + msg);
		}

	}

	public static void i(String tag, String msg)
	{
		checkBetaOrLine();
		if (V_DEBUG && msg != null)
		{
			android.util.Log.i(tag, msg);
		}

	}

	public static void i_write(String tag, String msg)
	{
		checkBetaOrLine();
		if (V_DEBUG && msg != null)
		{
			android.util.Log.v(tag, msg);
			FileUtils.writeTOfileAndActiveClear(PROJECT_LOG,
						DateFormat.format("yyyy-MM-dd HH:mm:ss", System.currentTimeMillis()) + "V<" + tag + ">---" + msg);
        }
	}

	public static void i(String tag, String msg, Throwable t)
	{
		checkBetaOrLine();
		if (V_DEBUG && msg != null)
		{
			android.util.Log.i(tag, msg, t);
		}

    }

	public static void i_write(String tag, String msg, Throwable t)
	{
		checkBetaOrLine();
		if (V_DEBUG && msg != null)
		{
			android.util.Log.i(tag, msg, t);
			FileUtils.writeTOfileAndActiveClear(PROJECT_LOG,
					DateFormat.format("yyyy-MM-dd HH:mm:ss", System.currentTimeMillis()) + "Vt<" + tag + ">---" + msg);
		}

	}

	public static void e(String tag, String msg)
	{
		checkBetaOrLine();
		if (V_DEBUG && msg != null)
		{
			android.util.Log.e(tag, msg);
		}

	}

	public static void e_write(String tag, String msg)
	{
		checkBetaOrLine();
		if (V_DEBUG && msg != null)
		{
			android.util.Log.e(tag, msg);
			FileUtils.writeTOfileAndActiveClear(PROJECT_LOG,
						DateFormat.format("yyyy-MM-dd HH:mm:ss", System.currentTimeMillis()) + "V<" + tag + ">---" + msg);
        }
	}


	/**
	 *
	 * @param tag
	 * @param logMsg
	 */
	public static void writeForBle(String tag , String logMsg)
	{
		checkBetaOrLine();
		if (V_DEBUG)
		{
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.append(DateFormat.format("yyyy-MM-dd HH:mm:ss", System.currentTimeMillis())).append("\t")
			             .append("< ").append(tag).append(" >").append("\t")
			             .append(logMsg).append("\t").append("\n");
			android.util.Log.e(tag, stringBuilder.toString());
			FileUtils.writeTOfileAndActiveClear(FileUtils.BLE_FILE_NAME,stringBuilder.toString());
        }
	}

	/**
	 *
	 * @param tag
	 * @param logMsg
	 */
	public static void writeForClassicBt(String tag , String logMsg)
	{
		checkBetaOrLine();
		if (V_DEBUG)
		{
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.append(DateFormat.format("yyyy-MM-dd HH:mm:ss", System.currentTimeMillis())).append("\t")
					.append("< ").append(tag).append(" >").append("\t")
					.append(logMsg).append("\t").append("\n");
			android.util.Log.e(tag, stringBuilder.toString());
			FileUtils.writeTOfileAndActiveClear(FileUtils.BLE_FILE_NAME,stringBuilder.toString());
		}
	}

	/**
	 *
	 * @param tag
	 * @param logMsg
	 */
	public static void writeComm(String tag , String fileName ,String logMsg)
	{
		checkBetaOrLine();
		if (true)
		{
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.append(DateFormat.format("yyyy-MM-dd HH:mm:ss", System.currentTimeMillis())).append("\t")
					.append("< ").append(tag).append(" >").append("\t")
					.append(logMsg).append("\t").append("\n");
			android.util.Log.e(tag, stringBuilder.toString());
			FileUtils.writeTOfileAndActiveClear(fileName,stringBuilder.toString());
		}
	}

	/**
	 *
	 * @param tag
	 * @param logMsg
	 */
	public static void writeForOTAStatic(String tag , String logMsg)
	{
		checkBetaOrLine();
		if (V_DEBUG)
		{
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.append(DateFormat.format("yyyy-MM-dd HH:mm:ss", System.currentTimeMillis())).append("\t")
					.append("< ").append(tag).append(" >").append("\t")
					.append(logMsg).append("\t").append("\n");
			android.util.Log.e(tag, stringBuilder.toString());
			FileUtils.writeTOfileAndActiveClear(FileUtils.OTA_STATIC,stringBuilder.toString());
		}
	}

	public static void e(String tag, String msg, Throwable t)
	{
		checkBetaOrLine();
		if (V_DEBUG && msg != null)
		{
			android.util.Log.e(tag, msg, t);
		}

    }

	public static void e_write(String tag, String msg, Throwable t)
	{
		checkBetaOrLine();
		if (V_DEBUG && msg != null)
		{
			android.util.Log.e(tag, msg, t);
			FileUtils.writeTOfileAndActiveClear(PROJECT_LOG,
					DateFormat.format("yyyy-MM-dd HH:mm:ss", System.currentTimeMillis()) + "Vt<" + tag + ">---" + msg);
		}

	}

	public static void d(String tag, String msg)
	{
		checkBetaOrLine();
		if (V_DEBUG && msg != null)
		{
			android.util.Log.d(tag, msg);
		}

	}

	public static void d_write(String tag, String msg)
	{
		checkBetaOrLine();
		if (V_DEBUG && msg != null)
		{
			android.util.Log.d(tag, msg);
			FileUtils.writeTOfileAndActiveClear(PROJECT_LOG,
						DateFormat.format("yyyy-MM-dd HH:mm:ss", System.currentTimeMillis()) + "V<" + tag + ">---" + msg);
        }
	}

	public static void d(String tag, String msg, Throwable t)
	{
		checkBetaOrLine();
		if (V_DEBUG && msg != null)
		{
			android.util.Log.d(tag, msg, t);
		}

    }

	public static void d_write(String tag, String msg, Throwable t)
	{
		checkBetaOrLine();
		if (V_DEBUG && msg != null)
		{
			android.util.Log.d(tag, msg, t);
			FileUtils.writeTOfileAndActiveClear(PROJECT_LOG,
					DateFormat.format("yyyy-MM-dd HH:mm:ss", System.currentTimeMillis()) + "Vt<" + tag + ">---" + msg);
		}

	}
	/**
	 * e 转 String
	 * @param ex
	 * @return
	 */
	public static String exToString(Exception ex){
		Writer writer = new StringWriter();
        PrintWriter printWriter = new PrintWriter(writer);
        ex.printStackTrace(printWriter);
        Throwable cause = ex.getCause();
        while (cause != null) {
            cause.printStackTrace(printWriter);
            cause = cause.getCause();
        }
        printWriter.close();
        String result = writer.toString();
        return result;
	}
}
