package com.bes_lib.utils;

import android.text.format.DateFormat;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.io.Writer;

/**
 * 日志工具类
 *
 * 该工具类提供了完整的日志管理功能，主要特性包括：
 * 1. 多级别日志控制（VERBOSE、INFO、ERROR、WARNING、DEBUG）
 * 2. 日志输出规则的动态配置
 * 3. 日志文件的本地保存和管理
 * 4. 专用日志文件分类（OTA、BLE、经典蓝牙等）
 * 5. 异常信息的格式化输出
 * 6. 时间戳自动添加功能
 *
 * 设计特点：
 * - 静态方法设计，全局可用
 * - 支持控制台输出和文件保存双重模式
 * - 灵活的日志级别开关控制
 * - 自动时间戳和标签格式化
 * - 专门的蓝牙通信日志支持
 *
 * 使用场景：
 * - 应用运行时的调试信息记录
 * - 蓝牙通信过程的详细日志
 * - OTA升级过程的状态跟踪
 * - 异常和错误信息的持久化保存
 * - 性能分析和问题排查
 *
 * 文件分类：
 * - Log.txt: 通用日志文件
 * - ota.txt: OTA升级相关日志
 * - HwCloudLog.txt: 华为云服务日志
 * - ble/spp: 蓝牙通信专用日志
 */
public class LogUtils
{
	/**
	 * OTA升级日志文件名
	 * 用于记录OTA升级过程中的所有操作和状态信息
	 */
	static String PROJECT_OTA = "ota.txt" ;

	/**
	 * 通用日志文件名
	 * 用于记录应用的一般运行日志和调试信息
	 */
	static String PROJECT_LOG = "Log.txt" ;

	/**
	 * 华为云服务日志文件名
	 * 用于记录与华为云服务交互的相关日志
	 */
	static String PROJECT_HW_CLOUD_LOG = "HwCloudLog.txt" ;

	/**
	 * VERBOSE级别日志开关
	 * 控制详细信息日志的输出
	 */
	static boolean V_DEBUG = true;

	/**
	 * INFO级别日志开关
	 * 控制一般信息日志的输出
	 */
	static boolean I_DEBUG = true;

	/**
	 * ERROR级别日志开关
	 * 控制错误信息日志的输出
	 */
	static boolean E_DEBUG = true;

	/**
	 * WARNING级别日志开关
	 * 控制警告信息日志的输出
	 */
	static boolean W_DEBUG = true;

	/**
	 * 检查并设置日志输出规则
	 *
	 * 内部方法，用于在Beta版本或线上版本中设置日志输出规则。
	 * 当前实现为开发模式，所有级别的日志都启用。
	 * 在正式发布时可以根据需要调整各级别的开关状态。
	 */
	private static void checkBetaOrLine(){
		// 开发阶段启用所有级别的日志
		V_DEBUG = true;
		I_DEBUG = true;
		E_DEBUG = true;
		W_DEBUG = true;
	}

	/**
	 * 初始化日志工具的输出规则
	 *
	 * 设置各个日志级别的开关状态，允许应用在运行时动态控制
	 * 哪些级别的日志需要输出，哪些需要屏蔽。
	 *
	 * @param w_log WARNING级别日志开关，true启用，false禁用
	 * @param v_log VERBOSE级别日志开关，true启用，false禁用
	 * @param i_log INFO级别日志开关，true启用，false禁用
	 * @param e_log ERROR级别日志开关，true启用，false禁用
	 */
	public static void InitLogUtils(boolean w_log ,boolean v_log, boolean i_log, boolean e_log)
	{
		// 设置各级别日志的开关状态
		V_DEBUG = v_log ;
		I_DEBUG = i_log ;
		E_DEBUG = e_log ;
		W_DEBUG = w_log ;
	}

	/**
	 * 输出VERBOSE级别日志（仅控制台）
	 *
	 * 输出详细级别的调试信息到控制台，不保存到文件。
	 * 适用于临时调试和开发阶段的信息输出。
	 *
	 * @param tag 日志标签，用于标识日志来源
	 * @param msg 日志消息内容
	 */
	public static void v(String tag, String msg)
	{
		// 检查日志输出规则
		checkBetaOrLine();

		// 检查VERBOSE级别开关和消息有效性
		if (V_DEBUG && msg != null)
		{
			// 输出到控制台
			android.util.Log.v(tag, msg);
		}
	}

	/**
	 * 输出VERBOSE级别日志（控制台+文件）
	 *
	 * 输出详细级别的调试信息到控制台，同时保存到日志文件。
	 * 适用于需要持久化记录的重要调试信息。
	 *
	 * @param tag 日志标签，用于标识日志来源
	 * @param msg 日志消息内容
	 */
	public static void v_write(String tag, String msg)
	{
		// 检查日志输出规则
		checkBetaOrLine();

		// 检查VERBOSE级别开关和消息有效性
		if (V_DEBUG && msg != null)
		{
			// 输出到控制台
			android.util.Log.v(tag, msg);

			// 格式化并保存到文件，包含时间戳、级别标识和标签
			FileUtils.writeTOfileAndActiveClear(PROJECT_LOG,
						DateFormat.format("yyyy-MM-dd HH:mm:ss", System.currentTimeMillis()) + "V<" + tag + ">---" + msg);
        }
	}

	/**
	 * 输出VERBOSE级别日志（仅控制台，带异常信息）
	 *
	 * 输出详细级别的调试信息和异常堆栈到控制台，不保存到文件。
	 * 适用于异常调试和错误分析。
	 *
	 * @param tag 日志标签，用于标识日志来源
	 * @param msg 日志消息内容
	 * @param t 异常对象，会输出完整的堆栈信息
	 */
	public static void v(String tag, String msg, Throwable t)
	{
		// 检查日志输出规则
		checkBetaOrLine();

		// 检查VERBOSE级别开关和消息有效性
		if (V_DEBUG && msg != null)
		{
			// 输出到控制台，包含异常堆栈
			android.util.Log.v(tag, msg, t);
		}
    }

	/**
	 * 输出VERBOSE级别日志（控制台+文件，带异常信息）
	 *
	 * 输出详细级别的调试信息和异常到控制台，同时保存到日志文件。
	 * 适用于需要持久化记录的异常信息和错误分析。
	 *
	 * @param tag 日志标签，用于标识日志来源
	 * @param msg 日志消息内容
	 * @param t 异常对象，会输出完整的堆栈信息
	 */
	public static void v_write(String tag, String msg, Throwable t)
	{
		// 检查日志输出规则
		checkBetaOrLine();

		// 检查VERBOSE级别开关和消息有效性
		if (V_DEBUG && msg != null)
		{
			// 输出到控制台，包含异常堆栈
			android.util.Log.v(tag, msg, t);

			// 格式化并保存到文件，标记为带异常的VERBOSE日志
			FileUtils.writeTOfileAndActiveClear(PROJECT_LOG,
					DateFormat.format("yyyy-MM-dd HH:mm:ss", System.currentTimeMillis()) + "Vt<" + tag + ">---" + msg);
		}
	}
	/**
	 * 输出WARNING级别日志（仅控制台）
	 *
	 * 输出警告级别的信息到控制台，不保存到文件。
	 * 适用于非致命性问题的提醒和警告信息。
	 *
	 * @param tag 日志标签，用于标识日志来源
	 * @param msg 日志消息内容
	 */
	public static void w(String tag, String msg)
	{
		// 检查日志输出规则
		checkBetaOrLine();

		// 检查WARNING级别开关和消息有效性
		if (W_DEBUG && msg != null)
		{
			// 输出到控制台
			android.util.Log.w(tag, msg);
		}
	}

	/**
	 * 输出WARNING级别日志（控制台+文件）
	 *
	 * 输出警告级别的信息到控制台，同时保存到日志文件。
	 * 适用于需要持久化记录的警告信息和潜在问题。
	 *
	 * @param tag 日志标签，用于标识日志来源
	 * @param msg 日志消息内容
	 */
	public static void w_write(String tag, String msg)
	{
		// 检查日志输出规则
		checkBetaOrLine();

		// 检查WARNING级别开关和消息有效性
		if (W_DEBUG && msg != null)
		{
			// 输出到控制台
			android.util.Log.w(tag, msg);

			// 格式化并保存到文件，标记为WARNING级别
			FileUtils.writeTOfileAndActiveClear(PROJECT_LOG,
						DateFormat.format("yyyy-MM-dd HH:mm:ss", System.currentTimeMillis()) + "W<" + tag + ">---" + msg);
        }
	}

	/**
	 * 输出WARNING级别日志（仅控制台，带异常信息）
	 *
	 * 输出警告级别的信息和异常堆栈到控制台，不保存到文件。
	 * 适用于非致命性异常的警告和问题分析。
	 *
	 * @param tag 日志标签，用于标识日志来源
	 * @param msg 日志消息内容
	 * @param t 异常对象，会输出完整的堆栈信息
	 */
	public static void w(String tag, String msg, Throwable t)
	{
		// 检查日志输出规则
		checkBetaOrLine();

		// 检查WARNING级别开关和消息有效性
		if (W_DEBUG && msg != null)
		{
			// 输出到控制台，包含异常堆栈
			android.util.Log.w(tag, msg, t);
		}
    }

	/**
	 * 输出WARNING级别日志（控制台+文件，带异常信息）
	 *
	 * 输出警告级别的信息和异常到控制台，同时保存到日志文件。
	 * 适用于需要持久化记录的警告异常和问题追踪。
	 *
	 * @param tag 日志标签，用于标识日志来源
	 * @param msg 日志消息内容
	 * @param t 异常对象，会输出完整的堆栈信息
	 */
	public static void w_write(String tag, String msg, Throwable t)
	{
		// 检查日志输出规则
		checkBetaOrLine();

		// 检查WARNING级别开关和消息有效性
		if (W_DEBUG && msg != null)
		{
			// 输出到控制台，包含异常堆栈
			android.util.Log.w(tag, msg, t);

			// 格式化并保存到文件，标记为带异常的WARNING日志
			FileUtils.writeTOfileAndActiveClear(PROJECT_LOG,
					DateFormat.format("yyyy-MM-dd HH:mm:ss", System.currentTimeMillis()) + "Wt<" + tag + ">---" + msg);
		}
	}

	public static void i(String tag, String msg)
	{
		checkBetaOrLine();
		if (V_DEBUG && msg != null)
		{
			android.util.Log.i(tag, msg);
		}

	}

	public static void i_write(String tag, String msg)
	{
		checkBetaOrLine();
		if (V_DEBUG && msg != null)
		{
			android.util.Log.v(tag, msg);
			FileUtils.writeTOfileAndActiveClear(PROJECT_LOG,
						DateFormat.format("yyyy-MM-dd HH:mm:ss", System.currentTimeMillis()) + "V<" + tag + ">---" + msg);
        }
	}

	public static void i(String tag, String msg, Throwable t)
	{
		checkBetaOrLine();
		if (V_DEBUG && msg != null)
		{
			android.util.Log.i(tag, msg, t);
		}

    }

	public static void i_write(String tag, String msg, Throwable t)
	{
		checkBetaOrLine();
		if (V_DEBUG && msg != null)
		{
			android.util.Log.i(tag, msg, t);
			FileUtils.writeTOfileAndActiveClear(PROJECT_LOG,
					DateFormat.format("yyyy-MM-dd HH:mm:ss", System.currentTimeMillis()) + "Vt<" + tag + ">---" + msg);
		}

	}

	public static void e(String tag, String msg)
	{
		checkBetaOrLine();
		if (V_DEBUG && msg != null)
		{
			android.util.Log.e(tag, msg);
		}

	}

	public static void e_write(String tag, String msg)
	{
		checkBetaOrLine();
		if (V_DEBUG && msg != null)
		{
			android.util.Log.e(tag, msg);
			FileUtils.writeTOfileAndActiveClear(PROJECT_LOG,
						DateFormat.format("yyyy-MM-dd HH:mm:ss", System.currentTimeMillis()) + "V<" + tag + ">---" + msg);
        }
	}


	/**
	 * 写入BLE蓝牙专用日志
	 *
	 * 专门用于记录BLE（低功耗蓝牙）相关的操作和通信日志。
	 * 日志会同时输出到控制台和保存到BLE专用日志文件中。
	 *
	 * 日志格式：时间戳 + 标签 + 消息内容
	 * 适用场景：BLE设备连接、数据传输、状态变化等
	 *
	 * @param tag 日志标签，通常为BLE操作的模块名或功能名
	 * @param logMsg BLE相关的日志消息内容
	 */
	public static void writeForBle(String tag , String logMsg)
	{
		// 检查日志输出规则
		checkBetaOrLine();

		// 检查调试开关
		if (V_DEBUG)
		{
			// 构建格式化的日志字符串
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.append(DateFormat.format("yyyy-MM-dd HH:mm:ss", System.currentTimeMillis())).append("\t") // 时间戳
			             .append("< ").append(tag).append(" >").append("\t") // 标签
			             .append(logMsg).append("\t").append("\n"); // 消息内容

			// 输出到控制台（使用ERROR级别确保显示）
			android.util.Log.e(tag, stringBuilder.toString());

			// 保存到BLE专用日志文件
			FileUtils.writeTOfileAndActiveClear(FileUtils.BLE_FILE_NAME,stringBuilder.toString());
        }
	}

	/**
	 * 写入经典蓝牙专用日志
	 *
	 * 专门用于记录经典蓝牙（Classic Bluetooth）相关的操作和通信日志。
	 * 日志会同时输出到控制台和保存到蓝牙专用日志文件中。
	 *
	 * 日志格式：时间戳 + 标签 + 消息内容
	 * 适用场景：经典蓝牙设备配对、SPP通信、音频传输等
	 *
	 * @param tag 日志标签，通常为经典蓝牙操作的模块名或功能名
	 * @param logMsg 经典蓝牙相关的日志消息内容
	 */
	public static void writeForClassicBt(String tag , String logMsg)
	{
		// 检查日志输出规则
		checkBetaOrLine();

		// 检查调试开关
		if (V_DEBUG)
		{
			// 构建格式化的日志字符串
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.append(DateFormat.format("yyyy-MM-dd HH:mm:ss", System.currentTimeMillis())).append("\t") // 时间戳
					.append("< ").append(tag).append(" >").append("\t") // 标签
					.append(logMsg).append("\t").append("\n"); // 消息内容

			// 输出到控制台（使用ERROR级别确保显示）
			android.util.Log.e(tag, stringBuilder.toString());

			// 保存到蓝牙专用日志文件（注意：这里应该使用SPP_FILE_NAME而不是BLE_FILE_NAME）
			FileUtils.writeTOfileAndActiveClear(FileUtils.BLE_FILE_NAME,stringBuilder.toString());
		}
	}

	/**
	 * 写入通用日志到指定文件
	 *
	 * 通用的日志写入方法，可以指定保存到任意文件名的日志文件中。
	 * 提供了最大的灵活性，适用于各种自定义日志需求。
	 *
	 * 日志格式：时间戳 + 标签 + 消息内容
	 * 适用场景：自定义模块日志、临时调试日志、特殊功能日志等
	 *
	 * @param tag 日志标签，用于标识日志来源
	 * @param fileName 目标日志文件名，不包含路径
	 * @param logMsg 要记录的日志消息内容
	 */
	public static void writeComm(String tag , String fileName ,String logMsg)
	{
		// 检查日志输出规则
		checkBetaOrLine();

		// 强制输出（if (true)表示总是执行）
		if (true)
		{
			// 构建格式化的日志字符串
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.append(DateFormat.format("yyyy-MM-dd HH:mm:ss", System.currentTimeMillis())).append("\t") // 时间戳
					.append("< ").append(tag).append(" >").append("\t") // 标签
					.append(logMsg).append("\t").append("\n"); // 消息内容

			// 输出到控制台（使用ERROR级别确保显示）
			android.util.Log.e(tag, stringBuilder.toString());

			// 保存到指定的日志文件
			FileUtils.writeTOfileAndActiveClear(fileName,stringBuilder.toString());
		}
	}

	/**
	 * 写入OTA升级统计日志
	 *
	 * 专门用于记录OTA（Over-The-Air）升级过程的统计信息和状态日志。
	 * 日志会同时输出到控制台和保存到OTA统计专用日志文件中。
	 *
	 * 日志格式：时间戳 + 标签 + 消息内容
	 * 适用场景：OTA升级进度、成功率统计、错误记录、性能分析等
	 *
	 * @param tag 日志标签，通常为OTA升级的阶段或操作名
	 * @param logMsg OTA升级相关的统计信息或状态消息
	 */
	public static void writeForOTAStatic(String tag , String logMsg)
	{
		// 检查日志输出规则
		checkBetaOrLine();

		// 检查调试开关
		if (V_DEBUG)
		{
			// 构建格式化的日志字符串
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.append(DateFormat.format("yyyy-MM-dd HH:mm:ss", System.currentTimeMillis())).append("\t") // 时间戳
					.append("< ").append(tag).append(" >").append("\t") // 标签
					.append(logMsg).append("\t").append("\n"); // 消息内容

			// 输出到控制台（使用ERROR级别确保显示）
			android.util.Log.e(tag, stringBuilder.toString());

			// 保存到OTA统计专用日志文件
			FileUtils.writeTOfileAndActiveClear(FileUtils.OTA_STATIC,stringBuilder.toString());
		}
	}

	public static void e(String tag, String msg, Throwable t)
	{
		checkBetaOrLine();
		if (V_DEBUG && msg != null)
		{
			android.util.Log.e(tag, msg, t);
		}

    }

	public static void e_write(String tag, String msg, Throwable t)
	{
		checkBetaOrLine();
		if (V_DEBUG && msg != null)
		{
			android.util.Log.e(tag, msg, t);
			FileUtils.writeTOfileAndActiveClear(PROJECT_LOG,
					DateFormat.format("yyyy-MM-dd HH:mm:ss", System.currentTimeMillis()) + "Vt<" + tag + ">---" + msg);
		}

	}

	public static void d(String tag, String msg)
	{
		checkBetaOrLine();
		if (V_DEBUG && msg != null)
		{
			android.util.Log.d(tag, msg);
		}

	}

	public static void d_write(String tag, String msg)
	{
		checkBetaOrLine();
		if (V_DEBUG && msg != null)
		{
			android.util.Log.d(tag, msg);
			FileUtils.writeTOfileAndActiveClear(PROJECT_LOG,
						DateFormat.format("yyyy-MM-dd HH:mm:ss", System.currentTimeMillis()) + "V<" + tag + ">---" + msg);
        }
	}

	public static void d(String tag, String msg, Throwable t)
	{
		checkBetaOrLine();
		if (V_DEBUG && msg != null)
		{
			android.util.Log.d(tag, msg, t);
		}

    }

	public static void d_write(String tag, String msg, Throwable t)
	{
		checkBetaOrLine();
		if (V_DEBUG && msg != null)
		{
			android.util.Log.d(tag, msg, t);
			FileUtils.writeTOfileAndActiveClear(PROJECT_LOG,
					DateFormat.format("yyyy-MM-dd HH:mm:ss", System.currentTimeMillis()) + "Vt<" + tag + ">---" + msg);
		}

	}
	/**
	 * 将异常对象转换为详细的字符串描述
	 *
	 * 将Exception异常及其完整的调用堆栈转换为字符串格式，
	 * 包括异常链中的所有原因异常信息。这对于日志记录、
	 * 错误报告和问题调试非常有用。
	 *
	 * 功能特点：
	 * - 获取完整的异常堆栈信息
	 * - 递归处理异常链中的所有原因异常
	 * - 格式化输出便于阅读和分析
	 * - 适用于各种类型的异常对象
	 *
	 * 使用场景：
	 * - 异常信息的日志记录
	 * - 错误报告的生成
	 * - 调试信息的收集
	 * - 异常信息的网络传输
	 *
	 * @param ex 要转换的异常对象
	 * @return 包含完整异常信息的字符串，包括堆栈跟踪和异常链
	 */
	public static String exToString(Exception ex){
		// 创建字符串写入器，用于收集异常信息
		Writer writer = new StringWriter();
        PrintWriter printWriter = new PrintWriter(writer);

        // 将主异常的堆栈跟踪写入到字符串写入器
        ex.printStackTrace(printWriter);

        // 获取异常链中的原因异常
        Throwable cause = ex.getCause();

        // 递归处理异常链中的所有原因异常
        while (cause != null) {
            // 将原因异常的堆栈跟踪也写入到字符串写入器
            cause.printStackTrace(printWriter);

            // 继续获取下一级原因异常
            cause = cause.getCause();
        }

        // 关闭打印写入器，确保所有数据都被写入
        printWriter.close();

        // 获取完整的异常信息字符串
        String result = writer.toString();
        return result;
	}
}
