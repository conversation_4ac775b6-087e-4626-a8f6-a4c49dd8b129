package com.bes_lib.bluetooth.scanner;

import android.annotation.TargetApi;
import android.bluetooth.le.BluetoothLeScanner;
import android.bluetooth.le.ScanResult;
import android.bluetooth.le.ScanSettings;
import android.content.Context;
import android.os.Build;

import com.bes_lib.bluetooth.callback.ScanCallback;
import com.bes_lib.utils.LogUtils;


/**
 * 低功耗蓝牙(BLE)扫描器 - Lollipop版本实现
 *
 * 该类为Android 5.0+ (API 21+) 版本提供BLE扫描功能。
 * 使用了Android新版本的BLE扫描API：BluetoothLeScanner和ScanCallback
 *
 * 主要特点：
 * 1. 使用BluetoothLeScanner.startScan()和stopScan()方法
 * 2. 支持ScanSettings配置扫描参数（扫描模式、功耗等）
 * 3. 通过ScanCallback接收扫描结果和错误信息
 * 4. 提供更好的性能和更丰富的功能
 * 5. 支持扫描过滤器和批量扫描结果
 *
 * 相比旧版API的优势：
 * - 更好的电池优化
 * - 更精确的扫描控制
 * - 支持扫描失败回调
 * - 更详细的扫描结果信息
 *
 * 注意：此类仅适用于Android 5.0及以上版本，
 * 对于较低版本需要使用LeJBScanner。
 *
 * Created by alloxuweibin on 2017/12/11.
 */
@TargetApi(Build.VERSION_CODES.LOLLIPOP)
public class LeLollipopScanner extends BaseScanner {

    /**
     * BLE扫描器实例
     * Android 5.0+版本的新BLE扫描API，提供更强大的扫描功能
     */
    private BluetoothLeScanner mLeScanner;

    /**
     * 构造方法
     *
     * 创建适用于Android 5.0+版本的BLE扫描器实例。
     * 在初始化时获取系统的BluetoothLeScanner实例，
     * 用于后续的BLE设备扫描操作。
     *
     * @param context Android上下文对象，用于获取蓝牙适配器
     */
    public LeLollipopScanner(Context context) {
        // 调用父类构造方法，初始化基础扫描器功能
        super(context);

        // 获取系统的BLE扫描器实例，用于执行具体的扫描操作
        mLeScanner = getBluetoothAdapter().getBluetoothLeScanner();
    }

    /**
     * 开始BLE设备扫描
     *
     * 使用Android 5.0+版本的BLE扫描API开始扫描低功耗蓝牙设备。
     * 此方法会检查当前扫描状态和扫描器可用性，配置扫描参数，
     * 然后启动扫描过程。
     *
     * @param callback 扫描结果回调接口
     */
    @Override
    public void startScan(ScanCallback callback) {
        // 调用父类方法保存回调接口
        super.startScan(callback);

        // 检查是否已在扫描中，避免重复启动
        if (isScanning())
            return;

        // 检查BLE扫描器是否可用，如果为空则重新获取
        if (mLeScanner == null) {
            mLeScanner = getBluetoothAdapter().getBluetoothLeScanner();
        }

        // 启动BLE扫描，使用低延迟扫描模式以获得更快的发现速度
        // 参数说明：
        // - null: 不使用扫描过滤器，扫描所有BLE设备
        // - ScanSettings: 配置扫描模式为低延迟模式
        // - mCallback: 扫描结果回调
        mLeScanner.startScan(null, new ScanSettings.Builder().setScanMode(ScanSettings.SCAN_MODE_LOW_LATENCY).build(), mCallback);

        // 触发扫描开始回调
        onScanStart();
    }

    /**
     * 停止BLE设备扫描
     *
     * 使用Android 5.0+版本的BLE扫描API停止正在进行的扫描。
     * 此方法会检查当前扫描状态，只有在扫描中时才执行停止操作。
     */
    @Override
    public void stopScan() {
        // 检查是否正在扫描，未扫描时直接返回
        if (!isScanning())
            return;

        // 停止BLE扫描，传入相同的回调对象
        mLeScanner.stopScan(mCallback);

        // 触发扫描结束回调
        onScanFinish();
    }

    /**
     * 关闭扫描器并释放资源
     *
     * 对于Lollipop版本的BLE扫描器，不需要特殊的资源清理操作，
     * 因为BluetoothLeScanner由系统管理，会自动处理资源释放。
     */
    @Override
    public void close() {
        // Lollipop版本的BLE扫描器无需特殊清理操作
        // BluetoothLeScanner由系统管理，会自动处理资源释放
    }

    /**
     * BLE扫描结果回调实现
     *
     * 这是Android 5.0+版本的BLE扫描回调接口实现。
     * 相比旧版API，新版回调提供了更丰富的功能：
     * - 支持扫描失败回调
     * - 提供更详细的扫描结果信息
     * - 支持批量扫描结果处理
     */
    private android.bluetooth.le.ScanCallback mCallback = new android.bluetooth.le.ScanCallback() {

        /**
         * 扫描失败回调方法
         *
         * 当BLE扫描启动失败时，系统会调用此方法。
         * 常见的失败原因包括：权限不足、蓝牙未开启、
         * 扫描器资源不足等。
         *
         * @param errorCode 错误代码，指示失败的具体原因
         */
        @Override
        public void onScanFailed(int errorCode) {
            // 记录扫描失败信息，便于调试和问题排查
            LogUtils.e(TAG, "onScanFailed " + errorCode);

            // 调用父类方法，执行默认的失败处理逻辑
            super.onScanFailed(errorCode);

            // 扫描失败时，触发扫描结束回调，通知上层应用
            onScanFinish();
        }

        /**
         * BLE设备扫描结果回调方法
         *
         * 当扫描到BLE设备时，Android系统会调用此方法。
         * 新版API提供了更丰富的扫描结果信息，包括
         * 回调类型、完整的扫描结果对象等。
         *
         * @param callbackType 回调类型，指示这是首次发现还是重复发现
         * @param result 完整的扫描结果对象，包含设备信息、信号强度、广告数据等
         */
        @Override
        public void onScanResult(int callbackType, ScanResult result) {
            // 记录发现的设备信息，便于调试和问题排查
            LogUtils.e(TAG, "onScanResult " + result.getDevice().toString());

            // 从扫描结果中提取设备信息并转发给上层应用
            // result.getDevice(): 获取发现的BLE设备对象
            // result.getRssi(): 获取信号强度
            // result.getScanRecord().getBytes(): 获取广告数据字节数组
            onFound(result.getDevice(), result.getRssi(), result.getScanRecord().getBytes());
        }
    };
}
