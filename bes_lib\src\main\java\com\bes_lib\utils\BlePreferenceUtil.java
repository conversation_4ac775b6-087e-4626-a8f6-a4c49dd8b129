       /*
        * Copyright (c) 2017 Baidu, Inc. All Rights Reserved.
        *
        * Licensed under the Apache License, Version 2.0 (the "License");
        * you may not use this file except in compliance with the License.
        * You may obtain a copy of the License at
        *
        *   http://www.apache.org/licenses/LICENSE-2.0
        *
        * Unless required by applicable law or agreed to in writing, software
        * distributed under the License is distributed on an "AS IS" BASIS,
        * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
        * See the License for the specific language governing permissions and
        * limitations under the License.
        */
       package com.bes_lib.utils;

       import android.content.Context;


       /**
        * BLE蓝牙配置偏好设置工具类
        *
        * 该类继承自PreferenceUtil，专门用于管理BLE蓝牙相关的配置信息，包括：
        * 1. BLE设备的MAC地址保存
        * 2. PCM音频数据保存状态管理
        * 3. BLE连接相关的配置参数
        * 4. 其他BLE特定的偏好设置
        *
        * 主要功能：
        * - 提供BLE专用的配置文件存储
        * - 管理音频数据保存开关状态
        * - 封装BLE相关的键值对操作
        *
        * 使用场景：
        * - 保存用户选择的BLE设备信息
        * - 记录音频录制和保存的偏好设置
        * - 存储BLE连接的历史配置
        *
        * <NAME_EMAIL> on 2017/6/17.
        */
       public class BlePreferenceUtil extends PreferenceUtil {

           /**
            * BLE配置文件名称
            * 所有BLE相关的配置都保存在这个SharedPreferences文件中
            */
           private static final String BLE_CONFIG = "ble_config";

           /**
            * PCM音频数据保存功能的键名
            * 用于标识是否启用PCM音频数据的本地保存功能
            */
           public static final String KEY_SAVE_PCM = "savePcm";

           /**
            * PCM保存功能启用状态值
            * 当PCM保存功能开启时使用此值
            */
           public static final String PCM_SAVE_ENABLE = "pcmSaveEnable";

           /**
            * PCM保存功能禁用状态值
            * 当PCM保存功能关闭时使用此值
            */
           public static final String PCM_SAVE_DISABLE = "pcmSaveDisable";

           /**
            * 保存BLE相关的配置数据
            *
            * 将指定的键值对保存到BLE专用的配置文件中。
            * 该方法是对父类put方法的封装，自动使用BLE_CONFIG作为文件名。
            *
            * 支持的数据类型：
            * - String: 字符串数据（如设备名称、MAC地址等）
            * - Integer: 整数数据（如连接超时时间等）
            * - Boolean: 布尔数据（如开关状态等）
            * - Float: 浮点数据（如信号强度阈值等）
            * - Long: 长整型数据（如时间戳等）
            *
            * @param context Android上下文对象，用于访问SharedPreferences
            * @param key 配置项的键名，建议使用类中定义的常量
            * @param object 要保存的值，支持多种数据类型
            */
           public static void put(Context context, String key, Object object) {
               // 调用父类方法，使用BLE专用配置文件名
               put(context, BLE_CONFIG, key, object);
           }

           /**
            * 读取BLE相关的配置数据
            *
            * 从BLE专用的配置文件中读取指定键的值。
            * 该方法是对父类get方法的封装，自动使用BLE_CONFIG作为文件名。
            *
            * 如果指定的键不存在，将返回提供的默认值。
            * 返回值的类型由defaultObject的类型决定。
            *
            * @param context Android上下文对象，用于访问SharedPreferences
            * @param key 配置项的键名，建议使用类中定义的常量
            * @param defaultObject 默认值，当键不存在时返回此值，同时决定返回值类型
            * @return 返回读取到的配置值，如果键不存在则返回默认值
            */
           public static Object get(Context context, String key, Object defaultObject) {
               // 调用父类方法，从BLE专用配置文件中读取数据
               return get(context, BLE_CONFIG, key, defaultObject);
           }
       }
