package com.bes.activity;

import static android.bluetooth.le.ScanSettings.SCAN_MODE_LOW_LATENCY;
import static android.content.ContentValues.TAG;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.le.BluetoothLeScanner;
import android.bluetooth.le.ScanResult;
import android.bluetooth.le.ScanSettings;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Paint;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.ListView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.bes.R;
import com.bes.service.BtHeleper;

import java.util.ArrayList;
import java.util.List;

/**
 * 蓝牙设备列表活动类
 *
 * 功能描述：
 * - 扫描并显示附近的蓝牙低功耗(BLE)设备
 * - 处理Android不同版本的蓝牙权限请求
 * - 提供设备选择界面，用户可以点击选择要连接的设备
 * - 支持Android 12及以上版本的新蓝牙权限模型
 *
 * 主要特性：
 * - 自动权限检查和请求
 * - 实时设备扫描和列表更新
 * - 设备去重显示
 * - 扫描结果回调处理
 *
 * 使用场景：
 * - 用户需要选择蓝牙设备进行连接
 * - 音频设备的发现和配对
 * - 蓝牙设备管理界面
 *
 * <AUTHOR>
 * @version 1.0
 */
public class FileListActivity extends Activity implements AdapterView.OnItemClickListener {
    /** 日志标签，使用类的简单名称 */
    protected final String TAG = getClass().getSimpleName();

    /** 设备列表视图，用于显示扫描到的蓝牙设备 */
    private ListView device_list_view;
    /** 自定义适配器，用于管理设备列表的显示 */
    private FileListAdapter mAdapter;
    /** 设备名称列表，存储扫描到的设备名称 */
    private List<String> deviceNameList = new ArrayList<>();
    /** 设备地址列表，存储扫描到的设备MAC地址 */
    private List<String> deviceAddressList = new ArrayList<>();

    /** 蓝牙适配器，用于蓝牙操作的核心对象 */
    private BluetoothAdapter mBluetoothAdapter;
    /** 蓝牙低功耗扫描器，用于扫描BLE设备 */
    private BluetoothLeScanner mLeScanner;

    /** 蓝牙权限请求码，用于权限回调识别 */
    private static final int REQUEST_BLUETOOTH_PERMISSIONS = 666;
    /** Android 12的SDK版本号是31，用于版本判断 */
    private static final int ANDROID_12_SDK = 31;
    /** 蓝牙扫描权限字符串常量，Android 12新增权限 */
    private static final String BLUETOOTH_SCAN = "android.permission.BLUETOOTH_SCAN";
    /** 蓝牙连接权限字符串常量，Android 12新增权限 */
    private static final String BLUETOOTH_CONNECT = "android.permission.BLUETOOTH_CONNECT";

    /**
     * 活动创建时的初始化方法
     * 设置布局、初始化UI组件、启动权限检查流程
     *
     * @param savedInstanceState 保存的实例状态，用于活动重建时恢复数据
     */
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 设置活动的布局文件
        setContentView(R.layout.activity_filelist);

        // 初始化设备列表视图
        device_list_view = (ListView) findViewById(R.id.file_list_view);
        // 创建并设置自定义适配器
        mAdapter = new FileListAdapter(this);
        device_list_view.setAdapter(mAdapter);
        // 设置列表项点击监听器
        device_list_view.setOnItemClickListener(this);

        // 开始检查和请求必要的权限
        checkAndRequestPermissions();
    }

    /**
     * 检查并请求蓝牙相关权限
     * 根据Android版本动态检查所需权限，并请求缺失的权限
     * Android 12以下版本只需要位置权限，Android 12及以上还需要蓝牙专用权限
     */
    private void checkAndRequestPermissions() {
        List<String> permissionsNeeded = new ArrayList<>();

        // 位置信息权限(蓝牙扫描需要) - 所有版本都需要
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_COARSE_LOCATION)
                != PackageManager.PERMISSION_GRANTED) {
            permissionsNeeded.add(Manifest.permission.ACCESS_COARSE_LOCATION);
        }

        // Android 12及以上版本需要额外的蓝牙权限
        if (Build.VERSION.SDK_INT >= ANDROID_12_SDK) {
            // 检查蓝牙扫描权限
            if (ContextCompat.checkSelfPermission(this, BLUETOOTH_SCAN)
                    != PackageManager.PERMISSION_GRANTED) {
                permissionsNeeded.add(BLUETOOTH_SCAN);
            }
            // 检查蓝牙连接权限
            if (ContextCompat.checkSelfPermission(this, BLUETOOTH_CONNECT)
                    != PackageManager.PERMISSION_GRANTED) {
                permissionsNeeded.add(BLUETOOTH_CONNECT);
            }
        }

        if (!permissionsNeeded.isEmpty()) {
            // 有缺失权限，请求用户授权
            ActivityCompat.requestPermissions(this,
                    permissionsNeeded.toArray(new String[0]),
                    REQUEST_BLUETOOTH_PERMISSIONS);
        } else {
            // 已有所有权限，直接开始扫描
            startScan();
        }
    }

    /**
     * 开始蓝牙低功耗设备扫描
     * 初始化蓝牙适配器和扫描器，配置扫描参数并启动扫描
     */
    private void startScan() {
        // 获取蓝牙适配器实例
        mBluetoothAdapter = BtHeleper.getBluetoothAdapter(this);
        if (mBluetoothAdapter == null) {
            Toast.makeText(this, "蓝牙不可用", Toast.LENGTH_SHORT).show();
            return;
        }

        // 获取蓝牙低功耗扫描器
        mLeScanner = mBluetoothAdapter.getBluetoothLeScanner();
        Log.i(TAG, "onCreate: -------------------" + mLeScanner);

        if (mLeScanner != null) {
            ScanSettings scanSettings = null;
            // Android 8.0及以上版本支持更高级的扫描设置
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                scanSettings = new ScanSettings.Builder()
                        .setScanMode(SCAN_MODE_LOW_LATENCY) // 低延迟扫描模式，快速发现设备
                        .setLegacy(false) // 使用新的扫描API
                        .setPhy(ScanSettings.PHY_LE_ALL_SUPPORTED) // 支持所有物理层
                        .setNumOfMatches(ScanSettings.MATCH_NUM_MAX_ADVERTISEMENT) // 最大匹配数量
                        .setCallbackType(ScanSettings.CALLBACK_TYPE_ALL_MATCHES) // 所有匹配都回调
                        .setMatchMode(ScanSettings.MATCH_MODE_AGGRESSIVE) // 激进匹配模式
                        .build();
            }
            Log.i(TAG, "onCreate: -------------------" + "startScan");
            try {
                // 开始扫描，不使用过滤器，使用配置的设置和回调
                mLeScanner.startScan(null, scanSettings, mLeScanCallBack);
            } catch (SecurityException e) {
                // 权限不足时的异常处理
                Log.e(TAG, "缺少必要权限: " + e.getMessage());
                Toast.makeText(this, "缺少必要权限，无法扫描蓝牙设备", Toast.LENGTH_SHORT).show();
                finish();
            }
        } else {
            Toast.makeText(this, "蓝牙扫描不可用", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 权限请求结果回调方法
     * 处理用户对权限请求的响应，决定是否继续扫描或退出
     *
     * @param requestCode 权限请求码，用于识别是哪个权限请求
     * @param permissions 请求的权限数组
     * @param grantResults 权限授予结果数组
     */
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == REQUEST_BLUETOOTH_PERMISSIONS) {
            // 检查是否所有权限都被授予
            boolean allPermissionsGranted = true;
            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allPermissionsGranted = false;
                    break;
                }
            }

            if (allPermissionsGranted) {
                // 所有权限都被授予，开始扫描
                startScan();
            } else {
                // 权限被拒绝，显示提示并退出
                Toast.makeText(this, "没有足够权限，无法扫描蓝牙设备", Toast.LENGTH_SHORT).show();
                finish();
            }
        }
    }

    /**
     * 蓝牙低功耗扫描回调对象
     * 处理扫描过程中的各种事件，包括设备发现和扫描失败
     * 使用@SuppressLint注解忽略权限检查警告，因为权限已在调用前检查
     */
    @SuppressLint("MissingPermission")
    private android.bluetooth.le.ScanCallback mLeScanCallBack = new android.bluetooth.le.ScanCallback() {

        /**
         * 扫描失败时的回调方法
         * 记录错误码用于调试
         *
         * @param errorCode 扫描失败的错误码
         */
        @Override
        public void onScanFailed(int errorCode) {
            super.onScanFailed(errorCode);
            Log.i(TAG, "onScanFailed: -----" + errorCode);
        }

        /**
         * 扫描到设备时的回调方法
         * 处理扫描结果，过滤有效设备并更新列表
         *
         * @param callbackType 回调类型
         * @param result 扫描结果，包含设备信息
         */
        @Override
        public void onScanResult(int callbackType, ScanResult result) {
            Log.i(TAG, "onScanResult ble: --------" + result.getDevice() + result.getDevice().getName());
            // 过滤掉没有名称的设备
            if (result.getDevice().getName() == null) {
                return;
            }
            // 检查设备是否已存在，避免重复添加
            if (!deviceAddressList.contains(result.getDevice().getAddress())) {
                deviceNameList.add(result.getDevice().getName()); // 添加设备名称
                deviceAddressList.add(result.getDevice().getAddress()); // 添加设备地址
                mAdapter.notifyDataSetChanged(); // 通知适配器数据已更改
            }
        }
    };

    /**
     * 活动销毁时的清理方法
     * 停止蓝牙扫描，释放资源，避免内存泄漏
     */
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mLeScanner != null) {
            try {
                // 停止蓝牙扫描，传入之前注册的回调对象
                mLeScanner.stopScan(mLeScanCallBack);
            } catch (Exception e) {
                // 捕获停止扫描时可能出现的异常
                Log.e(TAG, "停止扫描异常: " + e.getMessage());
            }
        }
    }

    /**
     * 列表项点击事件处理方法
     * 用户选择设备后，停止扫描并返回选中的设备信息
     *
     * @param parent 父视图（ListView）
     * @param view 被点击的视图项
     * @param position 被点击项在列表中的位置
     * @param id 被点击项的ID
     */
    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
        // 停止扫描，因为用户已经选择了设备
        if (mLeScanner != null) {
            try {
                mLeScanner.stopScan(mLeScanCallBack);
            } catch (Exception e) {
                Log.e(TAG, "停止扫描异常: " + e.getMessage());
            }
        }
        // 创建Intent返回选中的设备信息
        Intent intent = new Intent();
        intent.putExtra("deviceName", deviceNameList.get(position)); // 设备名称
        intent.putExtra("deviceAddress", deviceAddressList.get(position)); // 设备地址
        setResult(RESULT_OK, intent); // 设置返回结果
        finish(); // 结束当前活动
    }

    /**
     * 设备列表自定义适配器类
     * 继承BaseAdapter，用于管理蓝牙设备列表的显示
     * 实现ViewHolder模式以提高列表滚动性能
     */
    public class FileListAdapter extends BaseAdapter {
        /** 上下文对象，用于获取系统服务和资源 */
        private Context mContext;

        /**
         * 适配器构造方法
         *
         * @param context 上下文对象
         */
        public FileListAdapter(Context context) {
            mContext = context;
        }

        /**
         * 获取列表项总数
         *
         * @return 设备列表的大小
         */
        @Override
        public int getCount() {
            return deviceNameList.size();
        }

        /**
         * 获取指定位置的列表项数据
         *
         * @param position 列表项位置
         * @return 指定位置的设备名称
         */
        @Override
        public Object getItem(int position) {
            return deviceNameList.get(position);
        }

        /**
         * 获取指定位置的列表项ID
         *
         * @param position 列表项位置
         * @return 列表项ID（这里直接返回位置值）
         */
        @Override
        public long getItemId(int position) {
            return position;
        }

        /**
         * 创建和配置列表项视图
         * 使用ViewHolder模式优化性能，避免重复findViewById调用
         *
         * @param position 列表项位置
         * @param convertView 可复用的视图对象
         * @param parent 父视图组
         * @return 配置好的列表项视图
         */
        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            ViewHolder holder;
            if (convertView == null) {
                // 首次创建视图，需要inflate布局并创建ViewHolder
                convertView = LayoutInflater.from(mContext).inflate(R.layout.file_item, parent, false);
                holder = new ViewHolder();
                holder.mDeviceName = (TextView) convertView.findViewById(R.id.device_name);
                holder.mDeviceAddress = (TextView) convertView.findViewById(R.id.device_address);

                // 将ViewHolder存储在视图的tag中，便于复用
                convertView.setTag(holder);
            } else {
                // 复用已存在的视图，直接获取ViewHolder
                holder = (ViewHolder) convertView.getTag();
            }
            // 设置设备名称和地址到对应的TextView
            holder.mDeviceName.setText(deviceNameList.get(position));
            holder.mDeviceAddress.setText(deviceAddressList.get(position));

            return convertView;
        }

        /**
         * ViewHolder内部类
         * 用于缓存列表项中的视图组件，提高列表滚动性能
         * 避免重复调用findViewById方法
         */
        private class ViewHolder {
            /** 显示设备名称的TextView */
            TextView mDeviceName;
            /** 显示设备地址的TextView */
            TextView mDeviceAddress;
        }
    }
}
