package com.bes.activity;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.AlertDialog;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothProfile;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.provider.Settings;


import android.text.format.DateFormat;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.Toast;

import com.bes.Player.PcmUtils;
import com.bes.business.Observable.BaseInfo;
import com.bes.business.Observable.Event;
import com.bes.business.Observable.EventID;
import com.bes.business.Observable.PropertyObservable;

import com.bes.business.enty.CmdInfo;
import com.bes.constant.Constants;

import com.bes.R;
import com.bes.opus.OpusDecoder;
import com.bes.service.BleConnector;
import com.bes_lib.bluetooth.BtHelper;
import com.bes_lib.bluetooth.SppConnector;
import com.bes_lib.bluetooth.callback.ConnectCallback;
import com.bes_lib.utils.ArrayUtil;

import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;
import java.util.UUID;

import androidx.annotation.Nullable;
import androidx.annotation.NonNull;

/**
 * 主活动类 - 蓝牙音频处理应用的核心控制器
 *
 * 功能概述：
 * - 蓝牙设备连接管理（支持BLE和SPP两种连接方式）
 * - 音频数据的实时录制、传输和处理
 * - Opus音频编解码处理
 * - 立体声音频合成和播放
 * - 文件I/O操作和数据持久化
 * - 权限管理和用户交互
 *
 * 主要特性：
 * - 支持左右声道分离录制和播放
 * - 实时音频数据流处理
 * - 多种音频格式支持
 * - 设备状态监控和事件处理
 * - 音频质量和延迟监控
 *
 * 技术架构：
 * - 观察者模式：实现Event接口，响应系统事件
 * - 回调模式：实现ConnectCallback接口，处理连接状态
 * - 定时器机制：用于周期性数据处理和播放控制
 * - 文件流处理：支持大文件的分块读写
 *
 * <AUTHOR> on 01/09/2020.
 * @version 1.0
 */
public class MainActivity extends Activity implements Event, ConnectCallback {
    /** 日志标签，用于调试输出 */
    private final static String TAG = "MainActivity";

    // ==================== 蓝牙连接相关变量 ====================
    /** 蓝牙适配器，用于蓝牙操作的核心对象 */
    private BluetoothAdapter bluetoothAdapter = null;
    /** 当前连接的蓝牙设备对象 */
    private BluetoothDevice mDevice;
    /** SPP（串口协议）连接器，用于经典蓝牙连接 */
    SppConnector sppConnector = SppConnector.getConnector();
    /** BLE（低功耗蓝牙）连接器，用于低功耗蓝牙连接 */
    BleConnector bleConnector = BleConnector.getConnector(this, this);

    // ==================== UI控件变量 ====================
    /** 清除按钮，用于断开连接 */
    private Button clear;
    /** 解码器选择按钮 */
    private Button choosedecoder;
    /** BLE连接启动按钮 */
    private Button start_ble;
    /** SPP连接启动按钮 */
    private Button start_spp;
    /** 录制开始按钮 */
    private Button start_record;
    /** RSSI延迟读取按钮，显示信号延迟信息 */
    private Button rssi_read_delay;
    /** RSSI停止读取按钮，停止信号监测 */
    private Button rssi_read_stop;
    /** 左声道播放按钮 */
    private Button play_left;
    /** 右声道播放按钮 */
    private Button play_right;
    /** 立体声播放按钮 */
    private Button play_stereo;
    /** 当前带宽显示按钮 */
    private Button cur_bandwidth;

    // ==================== 状态标志变量 ====================
    /** 蓝牙连接按钮（通用） */
    private  Button  btBtn ;
    /** 蓝牙连接状态标志 */
    private  boolean  btConnected = false;
    /** 录制状态标志 */
    private  boolean  recording = false;
    /** 低带宽模式标志，true为低带宽，false为高带宽 */
    private  boolean  lowWidth = true;

    // ==================== 配置参数变量 ====================
    /** 音频数据宽度长度，用于数据分包处理 */
    private  int  widthLength = 0;
    /** 本地字节读取最大大小，10MB */
    private  int  getLocalBytesMaxSize = 1024 * 1024 * 10;
    /** 本地文件读取最大大小，10MB（10M播放54s，200播放1080s） */
    private  int  getLocalFileMaxSize = 1024 * 1024 * 10;
    /** 播放定时器间隔，40秒 */
    private  int  playTimeerInterval = 40000;
    /** 当前播放复合长度，记录播放进度 */
    private  int curPlayCompound = 0;

    /** 当前连接标志，-1未连接，1为BLE，2为SPP */
    private int curConnectFlag = -1;

    /** 立体声播放准备状态标志 */
    private  boolean  playSteroOk = false;

    // ==================== SharedPreferences键名常量 ====================
    /** 左声道解码长度的存储键名 */
    private  String curDecodeLengthL = "curDecodeLengthL";
    /** 右声道解码长度的存储键名 */
    private  String curDecodeLengthR = "curDecodeLengthR";
    /** 全部数据解码长度的存储键名 */
    private  String curDecodeLengthAll = "curDecodeLengthAll";
    /** 立体声合成长度的存储键名 */
    private String compoundStereoLength = "compoundStereoLength";

    /** SharedPreferences对象，用于数据持久化存储 */
    private SharedPreferences mSharedPreferences;
    /** SharedPreferences编辑器，用于编辑和保存数据 */
    private SharedPreferences.Editor mEditor;

    /** Opus音频解码器对象 */
    OpusDecoder opusDecoder;

    // ==================== 文件路径相关变量 ====================
    /** 左声道原始数据文件名 */
    private String dataL = "dataL";
    /** 右声道原始数据文件名 */
    private String dataR = "dataR";
    /** 全部原始数据文件名 */
    private String dataAll = "dataAll";
    /** 左声道解码数据文件名 */
    private String decodedataL = "decodedataL";
    /** 右声道解码数据文件名 */
    private String decodedataR = "decodedataR";
    /** 立体声解码数据文件名 */
    private String decodedataStereo = "decodedataStereo";

    /** 获取左声道数据文件完整路径 */
    private String getPathDataL(){return getDiskCacheDir(this) + dataL;}
    /** 获取右声道数据文件完整路径 */
    private String getPathDataR(){return getDiskCacheDir(this) + dataR;}
    /** 获取全部数据文件完整路径 */
    private String getPathDataAll(){return getDiskCacheDir(this) + dataAll;}
    /** 获取左声道解码数据文件完整路径 */
    private String getPathDecodedataL(){return getDiskCacheDir(this) + decodedataL;}
    /** 获取右声道解码数据文件完整路径 */
    private String getPathDecodedataR(){return getDiskCacheDir(this) + decodedataR;}
    /** 获取立体声解码数据文件完整路径 */
    private String getPathDecodedataStereo(){return getDiskCacheDir(this) + decodedataStereo;}
    // ==================== SharedPreferences数据访问方法 ====================
    /**
     * 获取左声道当前解码长度
     * @return 左声道已解码的字节长度，默认为0
     */
    private int getCurDecodeLengthL() {
        return mSharedPreferences.getInt(curDecodeLengthL, 0);
    }

    /**
     * 获取右声道当前解码长度
     * @return 右声道已解码的字节长度，默认为0
     */
    private int getCurDecodeLengthR() {
        return mSharedPreferences.getInt(curDecodeLengthR, 0);
    }

    /**
     * 获取全部数据当前解码长度
     * @return 全部数据已解码的字节长度，默认为0
     */
    private int getCurDecodeLengthAll() {
        return mSharedPreferences.getInt(curDecodeLengthAll, 0);
    }

    /**
     * 获取立体声合成长度
     * @return 立体声合成的字节长度，默认为0
     */
    private int getCompoundStereoLength() {
        return mSharedPreferences.getInt(compoundStereoLength, 0);
    }

    /**
     * 设置左声道解码长度并持久化保存
     * @param length 要保存的左声道解码长度
     */
    private void setCurDecodeLengthL(int length) {
        mEditor = mSharedPreferences.edit(); // 获取编辑器
        mEditor.putInt(curDecodeLengthL, length); // 存储数据
        mEditor.commit(); // 立即提交到存储
    }

    /**
     * 设置右声道解码长度并持久化保存
     * @param length 要保存的右声道解码长度
     */
    private void setCurDecodeLengthR(int length) {
        mEditor = mSharedPreferences.edit(); // 获取编辑器
        mEditor.putInt(curDecodeLengthR, length); // 存储数据
        mEditor.commit(); // 立即提交到存储
    }

    /**
     * 设置全部数据解码长度并持久化保存
     * @param length 要保存的全部数据解码长度
     */
    private void setCurDecodeLengthAll(int length) {
        mEditor = mSharedPreferences.edit(); // 获取编辑器
        mEditor.putInt(curDecodeLengthAll, length); // 存储数据
        mEditor.commit(); // 立即提交到存储
    }

    /**
     * 设置立体声合成长度并持久化保存
     * @param length 要保存的立体声合成长度
     */
    private void setCompoundStereoLength(int length) {
        mEditor = mSharedPreferences.edit(); // 获取编辑器
        mEditor.putInt(compoundStereoLength, length); // 存储数据
        mEditor.commit(); // 立即提交到存储
    }

    /** PCM音频工具类，用于音频播放和处理 */
    PcmUtils pcmUtils;

    // ==================== 定时器和控制变量 ====================
    /** 标记布尔值，用于控制某些操作的状态 */
    private boolean markBool = true;
    /** 主定时器对象，用于周期性任务调度 */
    private Timer timer;
    /** 主定时器任务对象，定义具体的执行任务 */
    private TimerTask task;

    /** 播放定时器对象，专门用于音频播放控制 */
    private Timer playTimer;
    /** 播放定时器任务对象，定义播放相关的执行任务 */
    private TimerTask playTask;

    // ==================== Android版本和权限常量 ====================
    /** Android 12的SDK版本号是31，用于版本判断 */
    private static final int ANDROID_12_SDK = 31;
    /** 蓝牙扫描权限字符串常量，Android 12新增权限 */
    private static final String BLUETOOTH_SCAN = "android.permission.BLUETOOTH_SCAN";
    /** 蓝牙连接权限字符串常量，Android 12新增权限 */
    private static final String BLUETOOTH_CONNECT = "android.permission.BLUETOOTH_CONNECT";

    /**
     * 初始化事件监听器
     * 注册各种系统事件的监听，包括蓝牙连接、数据接收、音频处理等事件
     */
    private void initEventListener(){
        // 定义需要监听的事件ID数组
        EventID[] eventIDs = new EventID[]{
                EventID.BT_DISCONNED,                    // 蓝牙断开连接事件
                EventID.BT_CONNECTED,                    // 蓝牙连接成功事件
                EventID.BT_CONNECT_ERROR,                // 蓝牙连接错误事件
                EventID.CMD_OP_BATTERY_STATUS_DISPLAY,   // 电池状态显示命令事件
                EventID.CMD_OP_MERIDIAN_EFFECT,          // 音效处理命令事件
                EventID.CMD_OP_EQ_SELECT,                // 均衡器选择命令事件
                EventID.CMD_OP_VOLUME_CONTROL_PLUS,      // 音量增加控制命令事件
                EventID.CMD_OP_VOLUME_CONTROL_DEC,       // 音量减少控制命令事件
                EventID.CMD_BT_SPP_DISCONNECT,           // SPP蓝牙断开命令事件
                EventID.UPDATA_RECE_RSSI_INFO,           // 接收RSSI信息更新事件（左声道）
                EventID.UPDATA_RECE_RSSI_INFO_R,         // 接收RSSI信息更新事件（右声道）
                EventID.UPDATA_RECE_RSSI_INFO_ALL,       // 接收RSSI信息更新事件（全部数据）
                EventID.UPDATA_RECE_DELAY_INFO,          // 接收延迟信息更新事件
                EventID.UPDATA_BAND_WIDTN,               // 带宽更新事件
                EventID.RECEIVE_START,                   // 开始接收数据事件
                EventID.RECEIVE_STOP,                    // 停止接收数据事件

        };
        // 将当前对象注册为这些事件的监听器
        PropertyObservable.getInstance().addListener(this, eventIDs);
    }

    /**
     * 事件更新视图方法
     * 实现Event接口的方法，处理各种系统事件的回调
     *
     * @param eventId 事件ID，标识具体的事件类型
     * @param baseInfo 基础信息对象
     * @param list 信息列表集合
     * @param obj 可变参数，传递事件相关的数据
     */
    @Override
    public void updateView(EventID eventId, BaseInfo baseInfo, Collection<? extends BaseInfo> list, Object... obj) {
        switch (eventId){
            case UPDATA_RECE_RSSI_INFO:
                // 处理左声道RSSI数据
                HandleData((byte[]) obj[0]);
                break;
            case UPDATA_RECE_RSSI_INFO_R:
                // 处理右声道RSSI数据
                HandleDataR((byte[]) obj[0]);
                break;
            case UPDATA_RECE_DELAY_INFO:
                // 更新延迟值显示
                // 将字节数组转换为小端序整数
                long i = ArrayUtil.bytesToIntLittle((byte[]) obj[0]);
                if (i == 0) return; // 忽略无效的延迟值
                // 更新延迟显示按钮的颜色和文本
                rssi_read_delay.setTextColor(Color.GREEN);
                rssi_read_delay.setText("delay report:" + i + " μs");
                break;
            case UPDATA_RECE_RSSI_INFO_ALL:
                // 处理全部RSSI数据，同时更新录制按钮状态
                if (start_record.getTextColors().getDefaultColor() != Color.GREEN) {
                    start_record.setTextColor(Color.GREEN); // 设置录制按钮为绿色表示正在录制
                }
                HandleDataAll((byte[]) obj[0]); // 处理全部数据
                break;
            case RECEIVE_START:
                // 数据接收开始事件
                showToast("receive start");
                break;
            case RECEIVE_STOP:
                // 数据接收停止事件
                showToast("receive stop");
                break;
            case BT_CONNECT_ERROR:
                // 蓝牙连接错误处理
                btConnected = false;
                btBtn.setText("Click to Connect SPP");
                btBtn.setTextColor(Color.RED); // 设置按钮为红色表示连接失败
                break;
            case BT_DISCONNED:
                // 蓝牙断开连接处理
                btConnected = false;
                btBtn.setText("Click to Connect SPP");
                btBtn.setTextColor(Color.RED); // 设置按钮为红色表示未连接
                break;
            case BT_CONNECTED:
                // 蓝牙连接成功处理
                btConnected = true;
                btBtn.setText("SPP is Connected");
                btBtn.setTextColor(Color.GREEN); // 设置按钮为绿色表示连接成功
                break;
        }
    }

    /**
     * 显示Toast消息（资源ID版本）
     * @param msg 字符串资源ID
     */
    protected void showToast(int msg) {
        Toast.makeText(this, msg, Toast.LENGTH_SHORT).show();
    }

    /**
     * 显示Toast消息（字符串版本）
     * @param msg 要显示的消息字符串
     */
    protected void showToast(String msg) {
        Toast.makeText(this, msg, Toast.LENGTH_SHORT).show();
    }

    @SuppressLint("MissingInflatedId")
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        initEventListener();

        start_ble = (Button)findViewById(R.id.start_ble);
        start_ble.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (curConnectFlag == 2) {
                    sppConnector.disconnect();
                }
                curConnectFlag = 1;

                // 先检查必要的权限
                if (Build.VERSION.SDK_INT >= ANDROID_12_SDK) {
                    // 对于Android 12及以上版本，检查BLUETOOTH_CONNECT和BLUETOOTH_SCAN权限
                    if (checkSelfPermission(BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED ||
                        checkSelfPermission(BLUETOOTH_SCAN) != PackageManager.PERMISSION_GRANTED) {

                        // 请求必要的蓝牙权限
                        requestPermissions(
                            new String[]{
                                BLUETOOTH_CONNECT,
                                BLUETOOTH_SCAN,
                                Manifest.permission.ACCESS_COARSE_LOCATION
                            },
                            888);
                        return;
                    }
                } else if (checkSelfPermission(Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
                    // Android 6.0-11版本需要位置权限
                    requestPermissions(new String[]{Manifest.permission.ACCESS_COARSE_LOCATION}, 888);
                    return;
                }

                // 权限检查通过，打开设备选择界面
                Intent intent = new Intent();
                intent.setClass(MainActivity.this, FileListActivity.class);
                MainActivity.this.startActivityForResult(intent, 777);
                MainActivity.this.overridePendingTransition(R.anim.slide_from_right, R.anim.slide_to_left);
            }
        });

        start_spp = (Button)findViewById(R.id.start_spp);
        start_spp.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (curConnectFlag == 1) {
                    bleConnector.disConnect();
                }
                curConnectFlag = 2;
                connectBtBlueTooth();
            }
        });

        clear = (Button)findViewById(R.id.clear);
        clear.setTextColor(Color.RED);
        clear.setOnClickListener(new View.OnClickListener()
        {
            @Override
            public void onClick(View v)
            {
                PropertyObservable.getInstance().fireEvent(EventID.CMD_BT_SPP_DISCONNECT, null , null , Constants.TYPE_SPP);

            }
        });


        saveBytesToFile(getPathDataL(), new byte[3]);

        btConnected = false;


        start_record = (Button)findViewById(R.id.start_record);
        start_record.setOnClickListener(new View.OnClickListener()
        {
            @Override
            public void onClick(View v) {
                if (recording) {
                    showToast("recording, please click STOP");
                    return;
                }

                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        startRecord();

                        byte rssiprofileType = (byte) 0x00;
                        CmdInfo rssipluscmdInfo = new CmdInfo(Constants.OP_TOTA_RSSI_READ_CMD, new byte[]{rssiprofileType});
                        if (sppConnector != null && sppConnector.isConnected()) {
                            Log.i(TAG, "updateView: ++++++");
                            sendSppMessage(rssipluscmdInfo.toBytes());
                        }
                        if (bleConnector != null && bleConnector.isConnected()) {
                            sendBleMessage(rssipluscmdInfo.toBytes());
                        }
                    }
                }, 2000);

              }
        });

        rssi_read_delay = (Button)findViewById(R.id.rssi_read_delay);

        rssi_read_stop = (Button)findViewById(R.id.rssi_read_stop);
        rssi_read_stop.setOnClickListener(new View.OnClickListener()
        {
            @Override
            public void onClick(View v)
            {
                saveText("onClick--Stop----" + recording);
                Log.i(TAG, "onClick: +++++++++111");
                if (recording == false) {
                    return;
                }
                recording = false;
                //8005
                byte stopType = (byte) 0x01;
                CmdInfo stopcmdInfo = new CmdInfo(Constants.OP_TOTA_RSSI_STOP_CMD, new byte[]{stopType});
                if (sppConnector != null && sppConnector.isConnected()) {
                    sendSppMessage(stopcmdInfo.toBytes());
                }
                if (bleConnector != null && bleConnector.isConnected()) {
                    sendBleMessage(stopcmdInfo.toBytes());
                }

                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        play_stereo.setTextColor(Color.GREEN);
                    }
                });
                //延时后最后进行解码
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        cutoffLocalAllFile();
                        checkLocalFile();
                        compoundStereo();

                        rssi_read_delay.setTextColor(Color.WHITE);
                        start_record.setTextColor(Color.WHITE);
                    }
                }, 1000);

            }
        });

        play_left = (Button)findViewById(R.id.play_left);
        play_left.setOnClickListener(new View.OnClickListener()
        {
            @Override
            public void onClick(View v)
            {

                if (recording) {
                    showToast("recording, please click STOP");
                    return;
                }
                //查看解码完成
                File fileL = new File(getPathDataL());
//                if (!fileL.exists()) {
//                    showToast("please click to record");
//                    return;
//                }
//                byte[] byteL = getbytesFromeLocalFile(getPathDataL());
//
//                if (byteL.length != getCurDecodeLengthL()) {
//                    showToast("decoding, please wait");
//                    return;
//                }

                //读取解码本地文件数据并播放 判断是否有可直接播放的文件
                File decodeFile = new File(getPathDecodedataL());
                if (decodeFile.exists()) {
                    //直接播放
                    player(getPathDecodedataL(), 0, true);
                } else if (fileL.exists()) {
                    decodeLocalDataAndPlay(getPathDataL(), getPathDecodedataL());
                } else {
                    showToast("please click to record");
                }
            }
        });

        play_right = (Button)findViewById(R.id.play_right);
        play_right.setOnClickListener(new View.OnClickListener()
        {
            @Override
            public void onClick(View v)
            {
                if (recording) {
                    showToast("recording, please click STOP");
                    return;
                }
                //查看解码完成
                File fileR = new File(getPathDataR());
//                if (!fileR.exists()) {
//                    showToast("please click to record");
//                    return;
//                }
//                byte[] byteR = getbytesFromeLocalFile(getPathDataR());
//                Log.i(TAG, "onClick: +++++" + byteR.length + "--" + getCurDecodeLengthR());
//                if (byteR.length != getCurDecodeLengthR()) {
//                    showToast("decoding, please wait");
//                    return;
//                }

                //读取解码本地文件数据并播放 判断是否有可直接播放的文件
                File decodeFile = new File(getPathDecodedataR());
                if (decodeFile.exists()) {
                    //直接播放
                   player(getPathDecodedataR(), 0, true);
                } else if (fileR.exists()){
                    decodeLocalDataAndPlay(getPathDataR(), getPathDecodedataR());
                }
            }
        });

        play_stereo = (Button)findViewById(R.id.play_stereo);
        play_stereo.setOnClickListener(new View.OnClickListener()
        {
            @Override
            public void onClick(View v)
            {

                if (recording) {
                    showToast("recording, please click STOP");
                    return;
                }

                File fileR = new File(getPathDataR());
                if (!fileR.exists()) {
                    showToast("please click to record");
                    return;
                }
//                byte[] byteR = getbytesFromeLocalFile(getPathDataR());
//                if (byteR.length != getCurDecodeLengthR()) {
//                    showToast("decoding, please wait");
//                    return;
//                }

                //读取解码本地文件数据并播放 判断是否有可直接播放的文件
                File fileL = new File(getPathDataL());
                File decodeFileS = new File(getPathDecodedataStereo());
                if (decodeFileS.exists()) {
//                    player(getPathDecodedataStereo(), 1, true);
//                    if (playSteroOk) {
//                        playSteroOk = false;
//                        pcmUtils.play(1);
//                    } else {
                        player(getPathDecodedataStereo(), 1, true);
//                    }
                } else if (fileL.exists())  {
                    compoundStereo ();

                    showToast("please click to record");
                }
            }
        });

        cur_bandwidth = (Button)findViewById(R.id.cur_bandwidth);
        cur_bandwidth.setOnClickListener(new View.OnClickListener()
        {
            @Override
            public void onClick(View v)
            {
               lowWidth = !lowWidth;
               if (lowWidth) {
                   cur_bandwidth.setText("" + (widthLength * 2));
               } else {
                   cur_bandwidth.setText("" + (widthLength * 2));
               }
            }
        });

        mSharedPreferences = getPreferences(Context.MODE_PRIVATE);


        pcmUtils = PcmUtils.getInstant();
        pcmUtils.resetPcmFile();

        //decoder


        //opus
        //根据具体数值计算
//        OpusDecoder.init(-1, -1, -1, -1, 64000);
//        opusDecoder = new OpusDecoder();
//        cutoffLocalAllFile();
//        checkLocalFile();
//        String s = this.getExternalFilesDir("").getAbsolutePath() + "/WatchAvs.txt";
//        Log.i(TAG, "onCreate: " + s);
//        File file = new File(s);
//        String ttt = getFileContent(file);
//        Log.i(TAG, "onCreate: ---" + ttt);
//        String test = "4b,41,7d,0b,e4,61,16,fe,bf,7c,2d,13,dd,8e,9d,d4,6b,6c,20,d5,a8,62,f5,88,83,e0,5a,57,2d,fe,f4,34,8e,3f,ac,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,4b,01,0c,1a,57,a0,75,28,a3,1d,5d,db,7c,9e,53,05,2b,55,b9,c2,85,6c,cf,d6,98,f9,ad,b1,59,20,71,a9,2c,3e,56,e8,be,50,79,c4,89,eb,a8,15,fa,31,16,ee,82,c4,2d,b7,d8,68,a4,b7,c1,a0,b1,6a,1c,b0,bc,8d,04,8b,94,c3,b5,ee,53,b5,4f,67,19,8f,d5,1c,4a,bd,f5,01,2a,0b,f8,29,80,dd,05,ef,41,1f,9c,7d,0a,bd,c9,88,64,d5,26,62,32,92,e3,26,f9,2f,6a,cc,2e,d0,7a,32,68,85,c3,87,16,ec,37,68,0c,0a,b2,0e,0e,ea,09,b9,cf,d4,c2,36,13,7a,e3,a5,7b,56,9b,50,4e,45,d9,99,2f,3e,bf,57,7a,4c,a2,49,9b,76,d9,a3,a7,cc,";
//        String t = ttt.replace("\n","");
//        Log.i(TAG, "test--" + t);
//        byte[] nt = ArrayUtil.toBytes(t);
//        Log.i(TAG, "nt---t--" + ArrayUtil.toHex(nt));
//        Log.i(TAG, "testdecode" + ArrayUtil.toHex(opusDecoder.decodeAll(nt)));

//        setCurDecodeLengthAll(0);
//        setCurDecodeLengthL(0);
//        setCurDecodeLengthR(0);
//        cutoffLocalAllFile();
//        checkLocalFile();
//        compoundStereo();

//        File fileL = new File(getPathDataL());
//
//        File fileR = new File(getPathDataR());
//
//        Log.i(TAG, "onCreate: +++++++" + fileL.length() + "---" + getCurDecodeLengthL());
//        Log.i(TAG, "onCreate: +++++++" + fileR.length() + "---" + getCurDecodeLengthR());
//
//
//        File fileAll = new File(getPathDataAll());
//        Log.i(TAG, "onCreate: +++++++" + fileAll.length() + "---" + getCurDecodeLengthAll());
//
//        File filedecodeAll = new File(getPathDecodedataStereo());
//        for (int i = 0; i <filedecodeAll.length() / 1000; i ++) {
//            byte[] bytes = new byte[1000];
//            System.arraycopy(getbytesFromeLocalFile(getPathDecodedataStereo()), i * 1000, bytes, 0, 1000);
//            Log.i(TAG, "onCreate: ++++" + ArrayUtil.toHex(bytes));
//        }
//        String s = ArrayUtil.toHex(getbytesFromeLocalFile(getPathDataL()));
//        saveTxtToFileWrite(getPathDecodedataL() + "str.txt", s);
//
//        String ss = ArrayUtil.toHex(getbytesFromeLocalFile(getPathDataL()));
//        saveTxtToFileWrite(getPathDecodedataR() + "str.txt", ss);
//        Log.i(TAG, "onCreate: +++++++" + filedecodeAll.length());
//        byte[] bytes = new  byte[]{0x00};
//        Log.i(TAG, "onCreate: +++++" + bytes[2]);


        saveText("init-------------");
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            if (requestCode == 777) {
                String deviceName = data.getStringExtra("deviceName");
                String deviceAddress = data.getStringExtra("deviceAddress");
                Log.i(TAG, "onActivityResult: -------------" + deviceName);
                Log.i(TAG, "onActivityResult: -------------" + deviceAddress);
                mDevice = BtHelper.getRemoteDevice(MainActivity.this, deviceAddress);
                bleConnector.startConnect(mDevice);
            }
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == 888) {
            // 检查是否所有权限都被授予
            boolean allPermissionsGranted = true;
            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allPermissionsGranted = false;
                    break;
                }
            }

            if (allPermissionsGranted) {
                // 所有权限都被授予，打开设备选择界面
                Intent intent = new Intent();
                intent.setClass(MainActivity.this, FileListActivity.class);
                MainActivity.this.startActivityForResult(intent, 777);
                MainActivity.this.overridePendingTransition(R.anim.slide_from_right, R.anim.slide_to_left);
            } else {
                // 未授予所有权限
                showToast("需要蓝牙权限才能连接设备");
            }
        }
    }

    /**
     * 使用FileWriter进行文本内容的追加
     * @param path
     * @param content
     */
    private void saveTxtToFileWrite(String path, String content){
        File file = new File(path);
        FileWriter writer = null;
        try {
            //FileWriter(file, true),第二个参数为true是追加内容，false是覆盖
            writer = new FileWriter(file, true);
            writer.write("\r\n");//换行
            writer.write(content);
            writer.flush();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if(writer != null){
                    writer.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

     private void startRecord() {
         saveText("startRecord");
        markBool = true;
        recording = true;
        widthLength = 0;

        pcmUtils.resetPcmFile();
        pcmUtils.stopPlay();

        if (playTimer != null) {
            playTimer.cancel();
            playTimer = null;
        }

        if (timer != null) {
            timer.cancel();
            timer = null;
        }
        initTimer();
        timer.schedule(task, 1, 3 * 1000);
        //+++/删除原文件
        playSteroOk = false;
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                play_stereo.setTextColor(Color.WHITE);

                deleteAudioFile(getPathDataL());
                deleteAudioFile(getPathDecodedataL());

                deleteAudioFile(getPathDataR());
                deleteAudioFile(getPathDecodedataR());

                deleteAudioFile(getPathDataAll());

                deleteAudioFile(getPathDecodedataStereo());

                setCurDecodeLengthL(0);
                setCurDecodeLengthR(0);
                setCurDecodeLengthAll(0);
                setCompoundStereoLength(0);

                play_right.setTextColor(Color.WHITE);
                play_left.setTextColor(Color.WHITE);
            }
        });



        //8004
//        PropertyObservable.getInstance().fireEvent(EventID.CMD_OP_RSSI_READ, null, null, lowWidth ? Constants.TYPE_SPP : Constants.TYPE_SPP_BW);
    }

    private void connectBtBlueTooth() {
        sppConnector.removeAllCallBack();
        sppConnector.addConnectCallback(MainActivity.this);

        getConnectBtDetails(getConnectBt());
    }

    //获取已连接的蓝牙设备名称
    private void getConnectBtDetails(int flag)
    {
        Log.i(TAG, "getConnectBtDetails: +++++++++");
        bluetoothAdapter.getProfileProxy(MainActivity.this, new BluetoothProfile.ServiceListener()
        {
            @Override
            public void onServiceDisconnected(int profile)
            {
                Log.i(TAG, "onServiceDisconnected: ++++++");

                Toast.makeText(MainActivity.this, profile + "", Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onServiceConnected(int profile, BluetoothProfile proxy)
            {
                Log.i(TAG, "onServiceConnected: +++++++++++");
                List<BluetoothDevice> mDevices = proxy.getConnectedDevices();
                if (mDevices != null && mDevices.size() > 0) {
                    for (BluetoothDevice device : mDevices) {

                        mDevice = BtHelper.getRemoteDevice(MainActivity.this, device.getAddress());
                        Log.i("getConnectBtDetails", "mDevice+++" + mDevice + "," + device.getAddress() + sppConnector);
                        if (mDevice != null) {
                            if (sppConnector.connect(mDevice)) {
                            }
                        }
                    }
                }
                else {
                    showToast("plase connect bluetooth first");
//                    updateConnectedBtAddress("请在手机端和耳机配对，以使用相应功能");
//                    updateConnectedBtName("--");
                }
            }
        }, flag);

    }

    //获取已连接的蓝牙设备状态
    private int getConnectBt()
    {
        if (bluetoothAdapter == null) {
            bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        }
        int a2dp = bluetoothAdapter.getProfileConnectionState(BluetoothProfile.A2DP);
        int headset = bluetoothAdapter.getProfileConnectionState(BluetoothProfile.HEADSET);
        int health = bluetoothAdapter.getProfileConnectionState(BluetoothProfile.HEALTH);
        int flag = -1;
        if (a2dp == BluetoothProfile.STATE_CONNECTED) {
            flag = a2dp;
        }
        else if (headset == BluetoothProfile.STATE_CONNECTED) {
            flag = headset;
        }
        else if (health == BluetoothProfile.STATE_CONNECTED) {
            flag = health;
        }
        Log.i(TAG, "getConnectBt flag: ++++++++" + flag);
        return flag;
    }


    void  initTimer()
    {
        timer = new Timer();
        task = new TimerTask()
        {
            @Override
            public void run()

            {
//                Log.i(TAG, "run: +++" + getCurDecodeLengthAll());
                if (recording == false) {
                    timer.cancel();
                    timer = null;
                    return;
                }
               if (markBool) {
                   markBool = false;
                   cutoffLocalAllFile();
               } else {
                   markBool = true;
                   checkLocalFile();
               }
            }
        };

    }

    private void cutoffLocalAllFile() {
        File fileAll = new File(getPathDataAll());
        if (!fileAll.exists()) {
            return;
        }
        int curDecodeL = getCurDecodeLengthAll();
        if (fileAll.length() - curDecodeL == 0) {
            return;
        }
        //分割左右数据
        final byte[] byteLast = getbytesFromeLocalFile(getPathDataAll(), curDecodeL, (int)(fileAll.length() - curDecodeL));
        setCurDecodeLengthAll(curDecodeL + byteLast.length);

        //4 + 160 + 160(a) + 4 + 160 + 160(a5)
//        int dataL = widthLength * 2;
        //4 + 160 + 4 + 160
        int dataL = widthLength;

        for (int i = 0; i < byteLast.length / (dataL  + 4); i++){
            byte[] handleData = getByteArr(byteLast, i * (dataL + 4) + 4, (i + 1) * (dataL + 4) - (lowWidth ? 0 : widthLength));
//            if (i % 2 == 0) {
//                    Log.i(TAG, "cutoffLocalAllFile左: +++++" + ArrayUtil.toHex(handleData));

                HandleData(handleData);
//            } else {
//                    Log.i(TAG, "cutoffLocalAllFile右: +++++" + ArrayUtil.toHex(handleData));

                HandleDataR(handleData);
//            }
        }
//            Log.i(TAG, "current run: +++" + getCurDecodeLengthAll());
    }

    private static byte[] getByteArr(byte[] data,int start ,int end){
        byte[] ret = new byte[end - start];
        for(int i = 0; (start + i) < end; i ++){
            ret[i] = data[start + i];
        }
        return ret;
    }

    private void checkLocalFile() {
        //检查本地文件长度
        File fileL = new File(getPathDataL());
        if (!fileL.exists()) {
            return;
        }

        int curDecodeLL = getCurDecodeLengthL();
        if (curDecodeLL == 0) {
            opusDecoder = new OpusDecoder();
        }

        if (curDecodeLL < fileL.length()) {
            byte[] byteL = getbytesFromeLocalFile(getPathDataL(), curDecodeLL, (int)(fileL.length() - curDecodeLL));
            checkDecodeDataAndSave(byteL, getPathDecodedataL());
            setCurDecodeLengthL(byteL.length + curDecodeLL);
        }

        File fileR = new File(getPathDataR());
        int curDecodeLR = getCurDecodeLengthR();

        if (curDecodeLR < fileR.length()) {
            byte[] byteR = getbytesFromeLocalFile(getPathDataR(), curDecodeLR, (int)(fileR.length() - curDecodeLR));
            checkDecodeDataAndSave(byteR, getPathDecodedataR());
            setCurDecodeLengthR(byteR.length + curDecodeLR);
        }
    }

    //解码
    private void checkDecodeDataAndSave(byte[] bytes, String savePath) {
        byte[] decoderOut = opusDecoder.decodeAll(bytes);
        saveBytesToFile(savePath, decoderOut);
    }



    @Override
    protected void onDestroy() {
        super.onDestroy();
        //断开后台服务
//        PropertyObservable.getInstance().fireEvent(EventID.CMD_OP_KEEP_ALIVE_STOP, null, null, Constants.TYPE_SPP);

        if(timer!=null)
        {
            timer.cancel();
            timer = null;
        }
    }

    private static final int REQUEST_SDCARD_PERMISSION = 0x01;

    void HandleData(final byte[] data)
    {
        saveBytesToFile(getPathDataL(), data);
    }

    void HandleDataR(final byte[] data)
    {
        saveBytesToFile(getPathDataR(), data);
    }

    void HandleDataAll(byte[] data)
    {
        saveBytesToFile(getPathDataAll(), data);
    }

    public void decodeLocalDataAndPlay(String filePath, String decodeFilePath) {
        byte[] bytes = getbytesFromeLocalFile(filePath, 0, 0);
        if (bytes.length > 0) {
//            Log.i(TAG, "decodeLocalDataAndPlay: ++++" + bytes.length);
            byte[] decoderOut = opusDecoder.decodeAll(bytes);

//            Log.i(TAG, "decodeLocalDataAndPlay: ++++" + ArrayUtil.toHex(decoderOut));
            //将解码后的文件存在本地
            deleteAudioFile(decodeFilePath);
            saveBytesToFile(decodeFilePath, decoderOut);

            //播放解码后的文件
           player(decodeFilePath, 0, true);
        }
    }

    public void decodeLocalData(String filePath, String decodeFilePath) {
        byte[] leftOut = getbytesFromeLocalFile(filePath, 0, 0);
        if (leftOut.length > 0) {
            byte[] decoderOut = opusDecoder.decodeAll(leftOut);

            //将解码后的文件存在本地
            deleteAudioFile(decodeFilePath);
            saveBytesToFile(decodeFilePath, decoderOut);
        }
    }

    //播放
    private void player(final String path, int type, boolean play) {
//        ShowProgressDialog.wait.dismiss();
        //直接播放
        File file = new File(path);
        if (file.length() > 0) {
            pcmUtils.resetPcmFile();
            pcmUtils.stopPlay();
            curPlayCompound = 0;
            playerAddData(path);
            playSteroOk = true;
            play_stereo.setTextColor(Color.GREEN);
            if (play) {
                pcmUtils.play(type);
                if (curPlayCompound < file.length()) {
                    playTimer = new Timer();
                    playTask = new TimerTask() {
                        @Override
                        public void run() {
                            playerAddData(path);
                        }
                    };
                    playTimer.schedule(playTask, playTimeerInterval, playTimeerInterval);
                }
            }
        }
    }

    private void playerAddData(String path) {
        File file = new File(path);
        if (file.length() == curPlayCompound || file.length() < curPlayCompound) {
            playTimer.cancel();
            playTimer = null;
            return;
        }
        long mark = file.length() - curPlayCompound > getLocalFileMaxSize ? curPlayCompound + getLocalFileMaxSize : file.length();

        FileAccessI fileAccess = null;
        try {
            fileAccess = new FileAccessI(path, 0);

        } catch (IOException e) {
            e.printStackTrace();
        }
        while (curPlayCompound < mark) {
            Log.i(TAG, "curCompoundL: -----" + curPlayCompound);
            if (file.length() - curPlayCompound > getLocalBytesMaxSize) {
                pcmUtils.addData(fileAccess.getContent(curPlayCompound, getLocalBytesMaxSize));
//                    pcmUtils.addData(getbytesFromeLocalFile(path, curCompoundL, getLocalBytesMaxSize));
                curPlayCompound += getLocalBytesMaxSize;
            } else {
                pcmUtils.addData(fileAccess.getContent(curPlayCompound, (int)(file.length() - curPlayCompound)));
//                    pcmUtils.addData(getbytesFromeLocalFile(path, curCompoundL, (int)(file.length() - curCompoundL)));
                curPlayCompound += (file.length() - curPlayCompound);
            }
        }
    }
    //合成立体声播放
    private void compoundStereo () {
        //左右声道解码
//        ShowProgressDialog.show(this, "decoding", new Thread());
        File decodeFileL = new File(getPathDecodedataL());

        if (decodeFileL.exists() == false) {
            return;
//            decodeLocalData(getPathDataL(), getPathDecodedataL());
        }

        File decodeFileR = new File(getPathDecodedataR());
        if (decodeFileR.exists() == false) {
//            decodeLocalData(getPathDataR(), getPathDecodedataR());
            return;
        }

        int curCompoundL = 0;
        while (curCompoundL < decodeFileL.length()) {
            //合为立体声
            byte[] leftOut;
            byte[] rightOut;
            FileAccessI fileAccessL = null;
            FileAccessI fileAccessR = null;
            try {
                fileAccessL = new FileAccessI(getPathDecodedataL(), 0);
                fileAccessR = new FileAccessI(getPathDecodedataR(), 0);

            } catch (IOException e) {
                e.printStackTrace();
            }
            if (decodeFileL.length() - curCompoundL > getLocalBytesMaxSize) {
                leftOut = fileAccessL.getContent(curCompoundL, getLocalBytesMaxSize);
                rightOut = fileAccessR.getContent(curCompoundL, getLocalBytesMaxSize);
//                leftOut = getbytesFromeLocalFile(getPathDecodedataL(), curCompoundL, getLocalBytesMaxSize);
//                rightOut = getbytesFromeLocalFile(getPathDecodedataR(), curCompoundL, getLocalBytesMaxSize);
                curCompoundL += getLocalBytesMaxSize;
            } else {
                leftOut = fileAccessL.getContent(curCompoundL, (int)(decodeFileL.length() - curCompoundL));
                rightOut = fileAccessR.getContent(curCompoundL, (int)(decodeFileL.length() - curCompoundL));
//                leftOut = getbytesFromeLocalFile(getPathDecodedataL(), curCompoundL, (int)(decodeFileL.length() - curCompoundL));
//                rightOut = getbytesFromeLocalFile(getPathDecodedataR(), curCompoundL, (int)(decodeFileL.length() - curCompoundL));
                curCompoundL += (decodeFileL.length() - curCompoundL);
            }
            byte[] bytes = new byte[rightOut.length * 2];
            for (int i = 0; i < ((leftOut.length > rightOut.length) ? rightOut.length / 2 : leftOut.length / 2); i ++) {
                bytes[i * 4] = leftOut[i * 2];
                bytes[i * 4 + 1] = leftOut[i * 2 + 1];
                bytes[i * 4 + 2] = rightOut[i * 2];
                bytes[i * 4 + 3] = rightOut[i * 2 + 1];
            }
            saveBytesToFile(getPathDecodedataStereo(), bytes);
        }
//        runOnUiThread(new Runnable() {
//            @Override
//            public void run() {
//                play_stereo.setTextColor(Color.GREEN);
//            }
//        });
    }

    //准备立体声播放
    private void readySteroPcmFile() {
        File fileStero = new File(getPathDecodedataStereo());
        if (fileStero.exists()) {
            player(getPathDecodedataStereo(), 1, false);
        }
    }

    //将data数据保存到本地文件夹
    public static void saveBytesToFile(String filePath, byte[] data) {
        if (data == null) {
            return;
        }
        File file = new File(filePath);
        BufferedOutputStream outStream = null;
        try {
            outStream = new BufferedOutputStream(new FileOutputStream(file, true));
            outStream.write(data);
            outStream.flush();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (null != outStream) {
                try {
                    outStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public String getDiskCacheDir(Context context) {
        String cachePath = null;
        if (Environment.MEDIA_MOUNTED.equals(Environment.getExternalStorageState())
                || !Environment.isExternalStorageRemovable()) {
            cachePath = context.getExternalCacheDir().getPath();
        } else {
            cachePath = context.getCacheDir().getPath();
        }
        return cachePath;
    }

    //删除原文件
    public void deleteAudioFile(String filePath) {
        File file = new File(filePath);
        if (file.isFile() && file.exists()) {
            file.delete();
        }
    }
    //将本地文件转化为byte[][]
    private byte[][] getbytesFromeLocalFiles(String path) {
        int size = 1024 * 1024 * 20;
        try {
            InputStream in = new FileInputStream(path);
            int dataSize = in.available();
            int num = dataSize / size + 1;
            Log.i(TAG, "dataSize / size + 1: ------" + num);
            byte[][] bytes = new byte[num][];
            for (int i = 0; i < num; i ++) {
                if ((i + 1) * size > dataSize) {
                    bytes[i] = new byte[size];
                    in.read(bytes[i], i * size, size);
                } else {
                    bytes[i] = new byte[dataSize - (i + 1) * size];
                    in.read(bytes[i], i * size, dataSize - (i + 1) * size);
                }
            }
            in.close();
            return bytes;
        } catch (IOException e) {
            Log.i(TAG, "++++" + e.toString());
            e.printStackTrace();
        }
        return new byte[0][];
    }

    //将本地文件转化为byte[]
    private byte[] getbytesFromeLocalFile(String path, int off, int size) {
        try {
            FileInputStream in = new FileInputStream(path);
            int l = in.available();
            Log.i(TAG, "getbytesFromeLocalFile: -----" + l);
            byte[] bytes = new byte[l];
            in.read(bytes, 0, l);
            in.close();
            int mL = size == 0 ? l : size;
            byte[] data = new byte[mL];
            Log.i(TAG, "ssss: --l:" + l + "--mL:" + mL + "--off:" + off + "--size:" + size);
            System.arraycopy(bytes, off, data, 0, mL);
            return data;
        } catch (IOException e) {
            Log.i(TAG, "++++" + e.toString());
            e.printStackTrace();
        }
        return new byte[0];
    }

    private byte[] toByteArray(InputStream in, int length) throws IOException {

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        byte[] buffer = new byte[length];
        int n = 0;
        while ((n = in.read(buffer)) != -1) {
            out.write(buffer, 0, n);
        }
        return out.toByteArray();
    }


    @Override
    public void onConnectionStateChanged(final boolean connected) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                showToast("spp is connected");
                if (connected) {
                    start_record.setTextColor(Color.BLUE);
                } else {
                    start_record.setTextColor(Color.RED);
                }
            }
        });
    }

    @Override
    public void onReceive(UUID uuid, byte[] data) {
        //初始delay值8000//12,80,04,00,12,53,ff,02,
        Log.i(TAG, "onReceive: -----------+++++" + data.length);
        Log.i(TAG, "onReceive: -----------+++++" + ArrayUtil.toHex(data));
        //64k  160 + 4   16k  40 + 4
        if (data.length > 20) {
            if (widthLength == 0) {
                widthLength = data.length - 4;
                int bitRate = 16000;
//                if (widthLength == 160) {
//                    bitRate = 64000;
//                } else if (widthLength == 80) {
//                    bitRate = 32000;
//                } else if (widthLength == 40) {
//                    bitRate = 16000;
//                }
                //opus
                //根据具体数值计算
                OpusDecoder.init(-1, -1, -1, -1, bitRate);
                opusDecoder = new OpusDecoder();
            }
            PropertyObservable.getInstance().fireEvent(EventID.UPDATA_RECE_RSSI_INFO_ALL, null, null, data);
            return;
        }
        saveText("receiveData:" + ArrayUtil.toHex(data));


        if (data.length == 5 && (data[0] & 0xFF) == 0x04 && (data[1] & 0xFF) == 0x80) {
            PropertyObservable.getInstance().fireEvent(EventID.RECEIVE_START, null, null, data);

            startRecord();
        } else if (data.length == 5 && (data[0] & 0xFF) == 0x05 && (data[1] & 0xFF) == 0x80) {
            PropertyObservable.getInstance().fireEvent(EventID.RECEIVE_STOP, null, null, data);


            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    play_stereo.setTextColor(Color.GREEN);
                }
            });
            recording = false;

            cutoffLocalAllFile();
            checkLocalFile();
            compoundStereo();

            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    rssi_read_delay.setTextColor(Color.WHITE);
                    start_record.setTextColor(Color.WHITE);
                }
            });
        } else if (data.length == 8 && (data[0] & 0xFF) == 0x12 && (data[1] & 0xFF) == 0x80) {
            byte[] delay = new byte[]{data[4], data[5], data[6], data[7]};
            PropertyObservable.getInstance().fireEvent(EventID.UPDATA_RECE_DELAY_INFO, null, null, delay);
        }
    }

    private boolean sendSppMessage(byte[] date)
    {
        if (date != null && date.length > 0) {
            boolean ret = sppConnector.write(date);
//            Log.i("sendMessage", "--------" + ret);
            Log.i(TAG, "sendMessage: ++++++++" + ArrayUtil.toHex(date));
            saveText("sendSppMessage--" + ret);

            if (!ret) {
                Log.i(TAG, "disconnect: ++++++++");

                sppConnector.disconnect();
            }
            return ret;
        }
        return false;
    }

    private boolean sendBleMessage(byte[] date) {
        boolean ret = bleConnector.write(date);
        saveText("sendBleMessage--" + ret);
        if (!ret) {
            bleConnector.disConnect();
        }
        return ret;
    }

    protected String getFileContent(File file) {
        String content = "";
        if (file.isDirectory()) {    //检查此路径名的文件是否是一个目录(文件夹)
            Log.i("11111", "The File doesn't not exist "
                    + file.getName().toString() + file.getPath().toString());
        } else {
            if (file.getName().endsWith(".txt")||file.getName().endsWith(".doc")) {//文件格式为txt文件
                try {
                    InputStream instream = new FileInputStream(file);
                    if (instream != null) {
                        InputStreamReader inputreader
                                = new InputStreamReader(instream, "GBK");
                        BufferedReader buffreader = new BufferedReader(inputreader);
                        String line = "";
                        //分行读取
                        while ((line = buffreader.readLine()) != null) {
                            content += line + "\n";
                        }
                        instream.close();        //关闭输入流
                    }
                } catch (FileNotFoundException e) {
                    Log.d("TestFile", "The File doesn't not exist.");
                } catch (IOException e) {
                    Log.d("TestFile", e.getMessage());
                }
            }
        }
        return content;
    }

    private void saveText(String text) {
        long curTimeMillis = System.currentTimeMillis();
        String path = getDiskCacheDir(this) + "/" + "files/";
        isExist(path);
        path = path + "log" + ".txt";
        File file = new File(path);
        try
        {
            if (!file.exists())
            {
                file.createNewFile();
            }
            FileInputStream fis = new FileInputStream(file);
            long size = fis.available();
            fis.close();
            /**
             * 当文件大小大于80MByte时，主动删除
             */
            if (size >= 80000000)
            {
                file.delete();
                return;
            }

            FileOutputStream stream = new FileOutputStream(file, true);
            String temp = DateFormat.format("yyyy-MM-dd HH:mm:ss:", curTimeMillis) + (curTimeMillis + "").substring(10, 13) + "   " + text + "\n";
            byte[] buf = temp.getBytes();
            stream.write(buf);
            stream.close();
        }
        catch (IOException e)
        {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

    }

    private static Object object = new Object();
    public static void isExist(String path)
    {
        File file = new File(path);
        // 判断文件夹是否存在,如果不存在则创建文件夹
        if (!file.exists())
        {
            synchronized (object)
            {
                file.mkdirs();
            }
        }
    }
}
