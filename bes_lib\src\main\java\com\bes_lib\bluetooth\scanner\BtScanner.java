package com.bes_lib.bluetooth.scanner;


import com.bes_lib.bluetooth.callback.ScanCallback;

/**
 * 蓝牙扫描器通用接口
 *
 * 该接口定义了蓝牙扫描器的基本操作方法，包括：
 * 1. 开始扫描
 * 2. 停止扫描
 * 3. 关闭扫描器
 *
 * 不同类型的蓝牙扫描器（经典蓝牙、低功耗蓝牙等）都需要实现此接口，
 * 以提供统一的扫描操作API。这样可以在不同的Android版本和蓝牙类型之间
 * 保持一致的使用方式。
 *
 * Created by alloxuweibin on 2017/12/11.
 */
public interface BtScanner {

    /**
     * 开始蓝牙设备扫描
     *
     * 启动蓝牙扫描过程，扫描结果将通过回调接口返回。
     * 在调用此方法前，需要确保蓝牙已开启且具有相应权限。
     *
     * @param callback 扫描结果回调接口，用于接收扫描过程中的各种事件通知
     *                包括扫描开始、发现设备、扫描结束等事件
     */
    void startScan(ScanCallback callback);

    /**
     * 停止蓝牙设备扫描
     *
     * 主动停止正在进行的蓝牙扫描过程。
     * 调用此方法后，将不再发现新的蓝牙设备，并触发扫描结束回调。
     */
    void stopScan();

    /**
     * 关闭蓝牙扫描器并释放资源
     *
     * 释放扫描器占用的系统资源，包括注销广播接收器、清理回调引用等。
     * 在不再需要使用扫描器时应调用此方法，以避免内存泄漏。
     */
    void close();
}
