package com.bes.service;

import static android.bluetooth.BluetoothGattCharacteristic.PROPERTY_NOTIFY;

import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattCallback;
import android.bluetooth.BluetoothGattCharacteristic;
import android.bluetooth.BluetoothGattDescriptor;
import android.bluetooth.BluetoothGattService;
import android.content.Context;
import android.os.Build;
import android.util.Log;

import com.bes_lib.bluetooth.callback.ConnectCallback;
import com.bes_lib.utils.ArrayUtil;

import java.util.ArrayList;
import java.util.UUID;

/**
 * BLE蓝牙连接器
 *
 * 功能描述：
 * - 管理BLE（蓝牙低功耗）设备的连接和通信
 * - 实现BES OPUS音频数据传输协议
 * - 提供完整的GATT服务发现和特征值操作
 * - 支持音频数据的双向传输和实时通信
 *
 * 设计模式：
 * - 单例模式：确保全局唯一的BLE连接管理器
 * - 回调模式：通过ConnectCallback接口通知连接状态和数据接收
 * - 状态机模式：管理连接的各种状态转换
 *
 * 核心功能：
 * - BLE设备连接和断开管理
 * - GATT服务发现和特征值配置
 * - MTU（最大传输单元）协商
 * - 特征值通知订阅和数据接收
 * - 音频数据的写入和传输
 *
 * 技术特点：
 * - 支持Android 8.0及以上版本的优化连接模式
 * - 自动重连机制，提高连接稳定性
 * - 完善的异常处理和错误恢复
 * - 支持512字节MTU的高速数据传输
 *
 * 使用场景：
 * - BES音频设备的蓝牙连接
 * - OPUS音频数据的实时传输
 * - 音频设备的控制命令发送
 * - 音频质量参数的实时监控
 *
 * <AUTHOR>
 * @version 1.0
 */
public class BleConnector {

    // ==================== BLE服务和特征值UUID常量 ====================

    /**
     * BES OPUS音频转储服务UUID
     * 用于标识BES设备的音频服务，所有音频相关操作都基于此服务
     */
    public static final UUID BES_OPUS_AUDIO_DUMP_SERVICE_UUID = UUID.fromString("65786365-6c70-6f69-6e74-2e636f820000");

    /**
     * BES OPUS音频转储特征值UUID（接收）
     * 用于接收来自BES设备的音频数据和状态信息
     */
    public static final UUID BES_OPUS_AUDIO_DUMP_CHARACTERISTIC_UUID = UUID.fromString("65786365-6c70-6f69-6e74-2e636f820003");

    /**
     * BES OPUS音频转储描述符UUID
     * 标准的客户端特征值配置描述符，用于启用通知功能
     */
    public static final UUID BES_OPUS_AUDIO_DUMP_DESCRIPTOR_UUID = UUID.fromString("00002902-0000-1000-8000-00805f9b34fb");

    /**
     * BES OPUS音频转储发送特征值UUID
     * 用于向BES设备发送音频数据和控制命令
     */
    public static final UUID BES_OPUS_AUDIO_DUMP_TX_UUID = UUID.fromString("65786365-6c70-6f69-6e74-2e636f820004");

    /**
     * 默认MTU大小（512字节）
     * MTU（Maximum Transmission Unit）最大传输单元
     * 较大的MTU可以提高数据传输效率，减少分包次数
     */
    public static final int DEFAULT_MTU = 512;

    // ==================== 成员变量 ====================

    /** 日志标签，使用类的简单名称 */
    protected final String TAG = getClass().getSimpleName();

    /**
     * BleConnector单例实例
     * 使用volatile关键字确保多线程环境下的可见性
     */
    private static volatile BleConnector mBleConnector;

    /** 应用程序上下文，用于BLE操作 */
    private static Context mContext;

    /** 连接状态回调接口，用于通知上层应用 */
    private static ConnectCallback mConnectCallback;

    /** 当前连接状态标志 */
    private boolean isConnect = false;

    /** 重连尝试标志，防止无限重连 */
    private boolean tryToReconnect = false;

    /** 当前连接的蓝牙设备 */
    private BluetoothDevice mDevice;

    /**
     * Android 8.0的SDK版本号
     * Android 8.0引入了BLE连接优化，支持指定传输类型
     */
    private static final int ANDROID_8_SDK = 26;

    /** 蓝牙GATT连接对象，用于所有GATT操作 */
    private static BluetoothGatt mBluetoothGatt;

    /** 发送数据的特征值对象 */
    BluetoothGattCharacteristic mCharacteristicTx;
    public static BleConnector getConnector(Context context, ConnectCallback connectCallback) {
        if (mBleConnector == null) {
            synchronized (BleConnector.class) {
                if (mBleConnector == null) {
                    mBleConnector = new BleConnector();
                }
            }
        }
        if (context != null) {
            mContext = context;
        }
        if (connectCallback != null) {
            mConnectCallback = connectCallback;
        }

        return mBleConnector;
    }

    public void startConnect(BluetoothDevice device) {
        if (device == null || mContext == null) {
            Log.e(TAG, "设备或上下文为空，无法连接");
            if (mConnectCallback != null) {
                mConnectCallback.onConnectionStateChanged(false);
            }
            return;
        }

        mDevice = device;
        isConnect = false;
        tryToReconnect = false;

        try {
            BluetoothGatt bluetoothGatt = null;
            if (Build.VERSION.SDK_INT >= ANDROID_8_SDK) {
                // Android 8及以上使用最高性能的BLE连接模式
                bluetoothGatt = device.connectGatt(mContext, false, mBluetoothGattCallback, BluetoothDevice.TRANSPORT_LE);
            } else {
                // 较老版本的Android使用基本连接
                bluetoothGatt = device.connectGatt(mContext, false, mBluetoothGattCallback);
            }

            if (bluetoothGatt == null) {
                Log.e(TAG, "创建GATT连接失败");
                if (mConnectCallback != null) {
                    mConnectCallback.onConnectionStateChanged(false);
                }
            }
        } catch (SecurityException e) {
            // 处理权限错误
            Log.e(TAG, "蓝牙连接权限错误: " + e.getMessage());
            if (mConnectCallback != null) {
                mConnectCallback.onConnectionStateChanged(false);
            }
        } catch (Exception e) {
            // 处理其他可能的错误
            Log.e(TAG, "蓝牙连接错误: " + e.getMessage());
            if (mConnectCallback != null) {
                mConnectCallback.onConnectionStateChanged(false);
            }
        }
    }

    private BluetoothGattCallback mBluetoothGattCallback = new BluetoothGattCallback() {
        @Override
        public void onConnectionStateChange(BluetoothGatt gatt, int status, int newState) {
            super.onConnectionStateChange(gatt, status, newState);
            Log.i(TAG, "onConnectionStateChange:-------------" + status);
            if (status == BluetoothGatt.GATT_SUCCESS && newState == BluetoothGatt.STATE_CONNECTED) {
                try {
                    gatt.discoverServices();
                } catch (Exception e) {
                    Log.e(TAG, "发现服务失败: " + e.getMessage());
                    if (mConnectCallback != null) {
                        mConnectCallback.onConnectionStateChanged(false);
                    }
                    isConnect = false;
                }
            } else {
                if (Build.VERSION.SDK_INT >= ANDROID_8_SDK && !tryToReconnect) {
                    tryToReconnect = true;
                    try {
                        BluetoothGatt bluetoothGatt = mDevice.connectGatt(mContext, false, mBluetoothGattCallback);
                        if (bluetoothGatt == null) {
                            Log.e(TAG, "重新连接失败");
                            if (mConnectCallback != null) {
                                mConnectCallback.onConnectionStateChanged(false);
                            }
                        }
                        return;
                    } catch (Exception e) {
                        Log.e(TAG, "重新连接过程出错: " + e.getMessage());
                    }
                }

                if (mConnectCallback != null) {
                    mConnectCallback.onConnectionStateChanged(false);
                }
                isConnect = false;
            }
        }

        @Override
        public void onServicesDiscovered(BluetoothGatt gatt, int status) {
            super.onServicesDiscovered(gatt, status);
            Log.i(TAG, "onServicesDiscovered: --------" + status);
            if (status == BluetoothGatt.GATT_SUCCESS) {
                if (setWriteCharacteristic(BES_OPUS_AUDIO_DUMP_SERVICE_UUID, BES_OPUS_AUDIO_DUMP_TX_UUID, gatt)) {
                    Log.i(TAG, "setWriteCharacteristic: --------");
                    try {
                        if (!gatt.requestMtu(DEFAULT_MTU)) {
                            enableCharacteristicNotification(BES_OPUS_AUDIO_DUMP_SERVICE_UUID, BES_OPUS_AUDIO_DUMP_CHARACTERISTIC_UUID, BES_OPUS_AUDIO_DUMP_DESCRIPTOR_UUID, gatt);
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "请求MTU失败: " + e.getMessage());
                        enableCharacteristicNotification(BES_OPUS_AUDIO_DUMP_SERVICE_UUID, BES_OPUS_AUDIO_DUMP_CHARACTERISTIC_UUID, BES_OPUS_AUDIO_DUMP_DESCRIPTOR_UUID, gatt);
                    }
                }
            } else {
                if (mConnectCallback != null) {
                    mConnectCallback.onConnectionStateChanged(false);
                }
            }
        }

        @Override
        public void onCharacteristicWrite(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic, int status) {
            super.onCharacteristicWrite(gatt, characteristic, status);
            Log.i(TAG, "onCharacteristicWrite: ----------" + status);
        }

        @Override
        public void onCharacteristicChanged(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic) {
            super.onCharacteristicChanged(gatt, characteristic);
            Log.i(TAG, "onCharacteristicChanged: -------------");
            if (mConnectCallback != null) {
                mConnectCallback.onReceive(null, characteristic.getValue());
            }
        }

        @Override
        public void onDescriptorWrite(BluetoothGatt gatt, BluetoothGattDescriptor descriptor, int status) {
            super.onDescriptorWrite(gatt, descriptor, status);
            Log.i(TAG, "onDescriptorWrite: ---------" + status);
            if (status == BluetoothGatt.GATT_SUCCESS) {
                isConnect = true;
                mBluetoothGatt = gatt;
                if (mConnectCallback != null) {
                    mConnectCallback.onConnectionStateChanged(true);
                }
                return;
            }
            if (mConnectCallback != null) {
                mConnectCallback.onConnectionStateChanged(false);
            }
        }

        @Override
        public void onMtuChanged(BluetoothGatt gatt, int mtu, int status) {
            super.onMtuChanged(gatt, mtu, status);
            enableCharacteristicNotification(BES_OPUS_AUDIO_DUMP_SERVICE_UUID, BES_OPUS_AUDIO_DUMP_CHARACTERISTIC_UUID, BES_OPUS_AUDIO_DUMP_DESCRIPTOR_UUID, gatt);
        }
    };

    private boolean setWriteCharacteristic(UUID service, UUID characteristic, BluetoothGatt gatt) {
        if (gatt == null) {
            return false;
        }
        BluetoothGattService gattService = gatt.getService(service);
        if (gattService == null) {
            return false;
        }
        BluetoothGattCharacteristic characteristicTx = gattService.getCharacteristic(characteristic);
        if (characteristicTx == null) {
            return false;
        }
        mCharacteristicTx = characteristicTx;

        return true;
    }

    private boolean enableCharacteristicNotification(UUID service, UUID rxCharacteristic, UUID descriptor, BluetoothGatt gatt) {
        Log.i(TAG, "enableCharacteristicNotification: ------------");
        if (gatt == null) {
            return false;
        }

        try {
            BluetoothGattService gattService = gatt.getService(service);
            if (gattService == null) {
                Log.e(TAG, "找不到指定服务");
                return false;
            }

            BluetoothGattCharacteristic gattCharacteristic = gattService.getCharacteristic(rxCharacteristic);
            if (gattCharacteristic == null) {
                Log.e(TAG, "找不到指定特征值");
                return false;
            }

            Log.i(TAG, "enableCharacteristicNotification: ----------22222");
            if (!gatt.setCharacteristicNotification(gattCharacteristic, true)) {
                Log.e(TAG, "设置特征值通知失败");
                return false;
            }

            Log.i(TAG, "enableCharacteristicNotification: ----------44444");
            BluetoothGattDescriptor gattDescriptor = gattCharacteristic.getDescriptor(descriptor);
            if (gattDescriptor == null) {
                Log.e(TAG, "找不到指定描述符");
                return false;
            }

            if (!gattDescriptor.setValue(BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE)) {
                Log.e(TAG, "设置描述符值失败");
                return false;
            }

            return gatt.writeDescriptor(gattDescriptor);
        } catch (Exception e) {
            Log.e(TAG, "启用特征值通知时出错: " + e.getMessage());
            return false;
        }
    }

    public boolean isConnected() {
        return isConnect;
    }

    public boolean write(byte[] data) {
        Log.i(TAG, "write: ---------" + ArrayUtil.toHex(data));
        if (mBluetoothGatt != null && mCharacteristicTx != null) {
            try {
                mCharacteristicTx.setValue(data);
                boolean ret = mBluetoothGatt.writeCharacteristic(mCharacteristicTx);
                Log.i(TAG, "write: ---------" + ret);
                return ret;
            } catch (Exception e) {
                Log.e(TAG, "写入特征值时出错: " + e.getMessage());
                return false;
            }
        }
        return false;
    }

    public void disConnect() {
        if (mBluetoothGatt != null) {
            try {
                mBluetoothGatt.disconnect();
                mBluetoothGatt.close();
            } catch (Exception e) {
                Log.e(TAG, "断开连接时出错: " + e.getMessage());
            }
        }
    }

}
