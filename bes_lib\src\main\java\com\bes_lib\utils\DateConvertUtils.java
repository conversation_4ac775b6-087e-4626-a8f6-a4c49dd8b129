package com.bes_lib.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

/**
 * 日期时间转换工具类
 *
 * 该工具类提供了日期时间格式转换的相关方法，主要用于：
 * 1. UTC时间戳到用户本地时间的转换
 * 2. 时间格式化显示
 * 3. 时间相关的工具方法
 *
 * 使用场景：
 * - 服务器返回的UTC时间戳需要转换为本地时间显示
 * - 需要按照特定格式显示时间
 * - 时间相关的数据处理
 */
public class DateConvertUtils {

	/**
	 * 根据标准时间戳转化为用户本地时间
	 *
	 * 将UTC时间戳转换为指定格式的本地时间字符串。
	 * 该方法会自动处理时区转换，确保显示的是用户本地时间。
	 *
	 * @param time UTC时间戳（毫秒），通常来自服务器或系统
	 * @param format 目标时间格式字符串，如"yyyy-MM-dd HH:mm:ss"
	 * @return 返回格式化后的本地时间字符串
	 *
	 * 示例：
	 * convertUTCToUser(1640995200000L, "yyyy-MM-dd HH:mm:ss")
	 * 返回："2022-01-01 08:00:00"（假设本地时区为GMT+8）
	 */
	public static String convertUTCToUser(long time, String format) {
		// 创建指定格式的日期格式化器
		SimpleDateFormat sDateFormat = new SimpleDateFormat(format);

		// 获取当前系统的日历实例，会自动使用本地时区
		Calendar calendar = Calendar.getInstance();

		// 设置时间戳到日历对象
		calendar.setTimeInMillis(time);

		// 格式化日期并返回字符串
		String dateString = sDateFormat.format(calendar.getTime());
		return dateString;
	}

}
