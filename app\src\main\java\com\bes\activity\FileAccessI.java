package com.bes.activity;

import java.io.IOException;
import java.io.RandomAccessFile;
import java.io.Serializable;

/**
 * 文件随机访问接口类
 *
 * 功能描述：
 * - 提供对文件的随机读写访问功能
 * - 支持从指定位置开始读取和写入数据
 * - 实现Serializable接口，支持对象序列化
 * - 主要用于音频数据文件的分块读写操作
 *
 * 设计模式：
 * - 封装了RandomAccessFile的复杂操作
 * - 提供简化的读写接口
 * - 内部包含Detail类用于数据传输
 *
 * 使用场景：
 * - 大文件的分块读取
 * - 音频数据的流式处理
 * - 需要从特定位置访问文件内容的场景
 *
 * <AUTHOR>
 * @version 1.0
 */
public class FileAccessI implements Serializable {
    /** 随机访问文件对象，用于实际的文件读写操作 */
    RandomAccessFile oSavedFile;
    /** 当前文件指针位置，记录下次读写的起始位置 */
    long nPos;

    /**
     * 默认构造方法
     * 创建一个空的文件访问对象，文件名为空字符串，起始位置为0
     *
     * @throws IOException 如果文件创建失败时抛出IO异常
     */
    public FileAccessI() throws IOException {
        this("", 0); // 调用带参数的构造方法，传入空文件名和0位置
    }

    /**
     * 带参数的构造方法
     * 创建指定文件的随机访问对象，并设置文件指针到指定位置
     *
     * @param sName 要访问的文件路径和名称
     * @param nPos 文件指针的起始位置（字节偏移量）
     * @throws IOException 如果文件不存在或无法访问时抛出IO异常
     */
    public FileAccessI(String sName, long nPos) throws IOException {
        // 创建一个随机访问文件类，使用读写模式("rw")
        oSavedFile = new RandomAccessFile(sName, "rw");
        this.nPos = nPos; // 保存当前位置
        oSavedFile.seek(nPos); // 将文件指针移动到指定位置
    }
    /**
     * 同步写入数据到文件
     * 使用synchronized关键字确保多线程环境下的写入安全
     *
     * @param b 要写入的字节数组
     * @param nStart 字节数组中的起始位置
     * @param nLen 要写入的字节长度
     * @return 实际写入的字节数，如果写入失败返回-1
     */
    public synchronized int write(byte[] b, int nStart, int nLen) {
        int n = -1; // 初始化返回值为-1，表示写入失败
        try {
            // 从字节数组的指定位置开始写入指定长度的数据
            oSavedFile.write(b, nStart, nLen);
            n = nLen; // 写入成功，返回实际写入的字节数
        }
        catch (IOException e) {
            // 捕获IO异常，打印错误堆栈信息
            e.printStackTrace();
        }
        return n; // 返回写入的字节数
    }
    /**
     * 同步读取文件内容
     * 从指定位置开始读取指定长度的数据，每次最多可读取102400字节
     * 使用synchronized关键字确保多线程环境下的读取安全
     *
     * @param nStart 文件中的起始读取位置（字节偏移量）
     * @param l 要读取的字节长度
     * @return 读取到的字节数组，如果读取失败返回空数组
     */
    public synchronized byte[] getContent(long nStart, int l) {
        Detail detail = new Detail(); // 创建Detail对象用于存储读取结果
        detail.b = new byte[l]; // 初始化字节数组，长度为要读取的字节数
        try {
            // 将文件指针移动到指定的起始位置
            oSavedFile.seek(nStart);
            // 从当前位置读取数据到字节数组中，并记录实际读取的字节数
            detail.length = oSavedFile.read(detail.b);
        }
        catch (IOException e) {
            // 捕获IO异常，打印错误堆栈信息
            e.printStackTrace();
        }
        return detail.b; // 返回读取到的字节数组
    }


    /**
     * 内部数据传输类
     * 用于封装文件读取操作的结果数据
     * 包含读取到的字节数组和实际读取的字节长度
     */
    public class Detail {
        /** 存储读取到的字节数据 */
        public byte[] b;
        /** 实际读取到的字节长度 */
        public int length;
    }

    /**
     * 获取文件长度
     * 返回当前文件的总字节数
     *
     * @return 文件的长度（字节数），如果获取失败返回0
     */
    public long getFileLength() {
        Long length = 0l; // 初始化文件长度为0
        try {
            // 获取随机访问文件的总长度
            length = oSavedFile.length();
        }
        catch (IOException e) {
            // 捕获IO异常，打印错误堆栈信息
            e.printStackTrace();
        }
        return length; // 返回文件长度
    }
}
