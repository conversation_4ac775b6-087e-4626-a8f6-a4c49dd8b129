package com.bes;

import android.app.Application;
import android.content.Intent;
import android.os.Handler;

import com.bes.business.Observable.AppConfig;

/**
 * BES应用程序主类
 *
 * 功能描述：
 * - 应用程序的入口点，负责全局初始化
 * - 管理应用程序级别的单例对象
 * - 初始化异常处理机制和全局Handler
 * - 提供应用程序实例的全局访问点
 *
 * 主要职责：
 * - 异常处理器的初始化和配置
 * - 全局Handler的创建和配置
 * - 蓝牙服务的启动准备
 * - 应用程序生命周期管理
 *
 * 设计模式：
 * - 单例模式：提供全局唯一的应用程序实例
 * - 初始化模式：在onCreate中完成所有必要的初始化工作
 *
 * 使用场景：
 * - 应用程序启动时的全局初始化
 * - 需要获取应用程序上下文的场景
 * - 全局异常处理和日志记录
 *
 * <AUTHOR>
 * @version 1.0
 *
 * 原始注释：
 * An application to handle all our initialization for the Alexa library before we
 * launch our VoiceLaunchActivity
 */
public class BesApplication extends Application {

    /**
     * Amazon应用程序产品ID
     * 用于服务器认证时的产品标识
     * 原始注释：Our Amazon application product ID, this is passed to the server when we authenticate
     */
    private static final String PRODUCT_ID = "besAlexa";

    /**
     * 崩溃异常处理器
     * 用于捕获和处理应用程序中的未捕获异常
     */
    CrashExceptionHandler crashExceptionHandler ;

    /**
     * 应用程序实例
     * 单例模式，提供全局访问点
     * 原始注释：Our Application instance if we need to reference it directly
     */
    private static BesApplication mInstance;

    /**
     * 全局Handler对象
     * 绑定到主线程Looper，用于线程间通信
     */
    public Handler handler ;

    /**
     * 应用程序创建时的初始化方法
     * 执行所有必要的全局初始化工作
     */
    @Override
    public void onCreate() {
        super.onCreate();
        mInstance = this; // 设置单例实例

        // 如果运行在DEBUG模式，可以在LogCat中获取签名密钥
        // 原始注释：if we run in DEBUG mode, we can get our signing key in the LogCat
        if(BuildConfig.DEBUG){
            // DEBUG模式下的特殊处理（当前为空实现）
        }

        // 初始化崩溃异常处理器
        crashExceptionHandler = CrashExceptionHandler.getInstance();
        crashExceptionHandler.init(getApplicationContext()); // 使用应用程序上下文初始化

        // 创建主线程Handler，用于线程间通信
        handler = new Handler(getApplicationContext().getMainLooper());
        AppConfig.mHandler = handler ; // 将Handler设置到全局配置中

        // 启动蓝牙服务
        startBtService();
    }

    /**
     * 启动蓝牙服务
     * 预留方法，用于初始化蓝牙相关服务
     * 当前为空实现，可根据需要添加蓝牙服务启动逻辑
     */
    private void startBtService(){
        // 蓝牙服务启动逻辑（当前为空实现）
    }

    /**
     * 获取应用程序实例
     * 单例模式的访问方法
     *
     * @return 当前应用程序实例，在onCreate()中创建
     *
     * 原始注释：
     * Return a reference to our mInstance instance
     * @return our current application instance, created in onCreate()
     */
    public static BesApplication getInstance(){
        return mInstance;
    }

}
