package com.bes_lib.bluetooth.callback;

import android.bluetooth.BluetoothDevice;

/**
 * 蓝牙设备扫描过程回调接口
 *
 * 该接口定义了蓝牙扫描过程中的三个核心回调方法：
 * 1. 发现新设备时的通知
 * 2. 扫描开始时的通知
 * 3. 扫描结束时的通知
 *
 * 实现此接口的类可以监听蓝牙扫描的整个生命周期，
 * 包括扫描状态变化和发现的蓝牙设备信息。
 *
 * Created by alloxuweibin on 2017/12/11.
 */
public interface ScanCallback {

    /**
     * 发现蓝牙设备时的回调方法
     *
     * 当扫描过程中发现新的蓝牙设备时，此方法会被调用。
     * 可以根据设备信息、信号强度和扫描记录来判断是否为目标设备。
     *
     * @param device 发现的蓝牙设备对象，包含设备名称、MAC地址等基本信息
     * @param rssi 接收信号强度指示器(RSSI)，单位为dBm，用于判断设备距离
     *             值越大表示信号越强，设备越近（通常为负值，如-30dBm比-80dBm更近）
     * @param scanRecord 设备的广播数据记录，包含设备的广告信息和服务UUID等
     *                   对于经典蓝牙可能为null，对于BLE设备包含丰富的广告数据
     */
    void onFound(BluetoothDevice device, int rssi, byte[] scanRecord);

    /**
     * 蓝牙扫描开始时的回调方法
     *
     * 当蓝牙扫描正式开始时，此方法会被调用。
     * 可以在此方法中更新UI状态，显示扫描进度指示器等。
     */
    void onScanStart();

    /**
     * 蓝牙扫描结束时的回调方法
     *
     * 当蓝牙扫描完成或被停止时，此方法会被调用。
     * 可以在此方法中隐藏扫描进度指示器，处理扫描结果等。
     */
    void onScanFinish();
}
