package com.bes.business.Observable;


import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map.Entry;
import java.util.Set;

/**
 * 多值HashMap实现类
 *
 * 功能描述：
 * - 允许一个Key对应多个值的Map数据结构
 * - 基于HashMap和ArrayList实现，提供类似Map的接口
 * - 支持泛型，提供类型安全的键值对存储
 *
 * 设计特点：
 * - 每个Key对应一个ArrayList，可以存储多个Value
 * - 自动处理Value的重复性检查，使用Value.equals()方法判断
 * - 提供与标准Map接口基本一致的操作方法
 * - 支持高效的批量操作和遍历
 *
 * 使用场景：
 * - 观察者模式中一个事件对应多个监听器
 * - 分类存储，一个分类下有多个项目
 * - 需要按键分组的数据结构
 *
 * 性能特点：
 * - 查询性能：O(1) - 基于HashMap的快速查找
 * - 插入性能：O(1) - 直接添加到对应的ArrayList
 * - 删除性能：O(n) - 需要遍历ArrayList查找目标值
 *
 * 注意事项：
 * - Value是否重复使用Value的equals()方法来判断
 * - 在put和remove方法中会用到Value.equals()方法
 * - 返回的ArrayList应该只进行读操作，避免直接修改
 *
 * <AUTHOR>
 * @version 1.0
 * @param <K> 键的类型
 * @param <V> 值的类型
 */
public class MultiHashMap<K, V>{
	/**
	 * 内部存储模型
	 * 使用HashMap存储Key到ArrayList的映射关系
	 * 每个Key对应一个ArrayList，用于存储多个Value
	 */
	private HashMap<K, ArrayList<V>> model;

	/**
	 * 默认构造方法
	 * 使用默认容量8初始化HashMap
	 */
	public MultiHashMap(){
		this(8); // 调用带参数的构造方法，默认容量为8
	}

	/**
	 * 带容量参数的构造方法
	 *
	 * @param capacity 初始容量，如果小于8则使用8作为最小容量
	 */
	public MultiHashMap(int capacity){
		// 确保最小容量为8，避免频繁扩容
		model = new HashMap<K, ArrayList<V>>(capacity < 8 ? 8 : capacity);
	}

	/**
	 * 清空所有数据
	 * 移除所有的键值对映射关系
	 */
	public void clear(){
		model.clear(); // 清空底层HashMap
	}

	/**
	 * 检查是否包含指定的键
	 *
	 * @param key 要检查的键
	 * @return 如果包含该键则返回true，否则返回false
	 */
	public boolean containsKey(K key){
		return model.containsKey(key); // 直接委托给HashMap的containsKey方法
	}

	/**
	 * 检查是否包含指定的值
	 * 注意：需要遍历所有的value集合，性能较差，时间复杂度为O(n*m)
	 * 其中n为键的数量，m为每个键对应值的平均数量
	 *
	 * @param value 要检查的值
	 * @return 如果任何一个键的值列表中包含该值则返回true，否则返回false
	 */
	public boolean containsValue(V value){
		// 遍历所有的键值对
		for(Entry<K, ArrayList<V>> e : model.entrySet()) {
			if(null != e.getValue()) { // 检查值列表是否为空
				return e.getValue().contains(value); // 在ArrayList中查找目标值
			}
		}
		return false; // 未找到目标值
	}

	/**
	 * 获取所有键值对的Entry集合
	 * 返回的Entry中，Key为原始键，Value为对应的ArrayList
	 *
	 * @return 包含所有键值对的Set集合
	 */
	public Set<Entry<K, ArrayList<V>>> entrySet(){
		return model.entrySet(); // 直接返回HashMap的entrySet
	}

	/**
	 * 根据Key查找对应的Value集合
	 *
	 * 重要提示：应该对返回的结果集只进行读操作，避免直接修改
	 * 如果需要修改，请使用put、remove等方法
	 *
	 * @param key 要查找的键
	 * @return 对应的值列表，如果键不存在则返回null
	 */
	public ArrayList<V> get(K key){
		return model.get(key); // 直接委托给HashMap的get方法
	}

	/**
	 * 检查MultiHashMap是否为空
	 *
	 * @return 如果不包含任何键值对则返回true，否则返回false
	 */
	public boolean isEmpty(){
		return model.isEmpty(); // 直接委托给HashMap的isEmpty方法
	}

	/**
	 * 获取所有键的集合
	 *
	 * @return 包含所有键的Set集合
	 */
	public Set<K> keySet(){
		return model.keySet(); // 直接委托给HashMap的keySet方法
	}

	/**
	 * 向指定键添加一个值
	 * 如果键不存在，会自动创建新的ArrayList
	 * 如果值已存在，则不会重复添加（基于equals方法判断）
	 *
	 * @param key 要添加值的键
	 * @param value 要添加的值
	 */
	public void put(K key, V value){
		ArrayList<V> ls = model.get(key); // 获取键对应的值列表
		if (null == ls){ // 如果键不存在
			ls = new ArrayList<V>(); // 创建新的ArrayList
			model.put(key, ls); // 将键和新列表添加到HashMap中
		}
		if (!ls.contains(value)){ // 检查值是否已存在（避免重复）
			ls.add(value); // 添加新值到列表中
		}
	}

	/**
	 * 移除指定键及其对应的所有值
	 *
	 * @param key 要移除的键
	 * @return 被移除的值列表，如果键不存在则返回null
	 */
	public ArrayList<V> remove(K key){
		return model.remove(key); // 直接委托给HashMap的remove方法
	}

	/**
	 * 从指定键的值列表中移除特定值
	 * 如果移除后列表为空，键仍然保留（与标准Map行为不同）
	 *
	 * @param key 目标键
	 * @param val 要移除的值
	 */
	public void remove(K key, V val){
		ArrayList<V> ls = model.get(key); // 获取键对应的值列表
		if (null != ls){ // 如果列表存在
			ls.remove(val); // 从列表中移除指定值
		}
	}

	/**
	 * 从所有键的值列表中移除指定值
	 * 注意：低效率操作，需要遍历所有键的值列表
	 * 时间复杂度为O(n*m)，其中n为键的数量，m为每个键对应值的平均数量
	 *
	 * @param val 要移除的值
	 */
	public void removeValue(V val){
		// 遍历所有的键值对
		for(Entry<K, ArrayList<V>> e : model.entrySet()) {
			if(null != e.getValue()) { // 检查值列表是否为空
				e.getValue().remove(val); // 从每个列表中移除指定值
			}
		}
	}

	/**
	 * 获取键的数量
	 * 注意：这里返回的是键的数量，不是所有值的总数量
	 *
	 * @return 键的数量
	 */
	public int size(){
		return model.size(); // 直接委托给HashMap的size方法
	}

	/**
	 * 获取所有值列表的集合
	 * 返回的Collection中每个元素都是一个ArrayList
	 *
	 * @return 包含所有值列表的Collection
	 */
	public Collection<ArrayList<V>> values(){
		return model.values(); // 直接委托给HashMap的values方法
	}

}